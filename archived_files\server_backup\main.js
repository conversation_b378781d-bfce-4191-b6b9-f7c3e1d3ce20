require('dotenv').config();
const path = require('path');
const { fork } = require('child_process');
const { connect, saveResult } = require('./db/mongo');
const fallbackAPIs = require('./fallback/fallbackHandler');
const { API_PRIORITY } = require('./config');

// Application state
const app = {
  isRunning: false,
  server: null,
  apiClients: {},
  intervalIds: {}
};

/**
 * Validate required API keys
 * @returns {Object} - Object with validation results
 */
function validateApiKeys() {
  const keys = {
    ALPHA_VANTAGE_API_KEY: process.env.ALPHA_VANTAGE_API_KEY,
    TWELVE_DATA_API_KEY: process.env.TWELVE_DATA_API_KEY,
    FINNHUB_API_KEY: process.env.FINNHUB_API_KEY,
    FRED_API_KEY: process.env.FRED_API_KEY,
    POLYGON_API_KEY: process.env.POLYGON_API_KEY,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    FMP_API_KEY: process.env.FMP_API_KEY
  };

  const results = {
    valid: true,
    missing: [],
    messages: []
  };

  for (const [name, value] of Object.entries(keys)) {
    if (!value || value === 'demo' || value.trim() === '') {
      results.valid = false;
      results.missing.push(name);
      results.messages.push(`${name} is not set or using demo value. Get a valid API key for production use.`);
    }
  }

  return results;
}

/**
 * Get market data from API
 * @param {string} type - Market type (forex, stocks, etc)
 * @param {Object} query - Query parameters
 * @returns {Promise<Object>} - Market data
 */
async function getData(type, query) {
  console.log(`Getting data for ${type}: ${JSON.stringify(query)}`);
  try {
    const apiOrder = API_PRIORITY[type];
    if (!apiOrder || !Array.isArray(apiOrder) || apiOrder.length === 0) {
      throw new Error(`No API clients configured for market type: ${type}`);
    }
    
    const result = await fallbackAPIs(apiOrder, query);
    
    // Check if result data is empty
    if (!result || (result.data && result.data.length === 0)) {
      console.warn(`Warning: Empty data received for ${type}:${query.symbol}`);
    }
    
    // Store result in database
    await saveResult(type, { 
      query, 
      result, 
      date: new Date() 
    });
    
    return result;
  } catch (error) {
    console.error(`Error getting data for ${type}:`, error);
    throw error;
  }
}

/**
 * Start the application server
 */
async function startServer() {
  if (app.server) {
    console.log('Server is already running');
    return;
  }
  
  try {
    // Start server in a separate process
    app.server = fork(path.join(__dirname, 'server.js'));
    
    app.server.on('message', (message) => {
      console.log('Message from server:', message);
    });
    
    app.server.on('error', (error) => {
      console.error('Server error:', error);
      app.server = null;
    });
    
    app.server.on('exit', (code, signal) => {
      console.log(`Server process exited with code ${code} and signal ${signal}`);
      app.server = null;
    });
    
    console.log('Server started');
  } catch (error) {
    console.error('Failed to start server:', error);
    throw error;
  }
}

/**
 * Initialize the application
 */
async function init() {
  if (app.isRunning) {
    console.log('Application is already running');
    return;
  }
  
  try {
    console.log('Initializing Trading Signals App...');
    
    // Validate API keys
    const keyValidation = validateApiKeys();
    if (!keyValidation.valid) {
      console.warn('WARNING: Some API keys are missing or using demo values:');
      keyValidation.messages.forEach(msg => console.warn(`- ${msg}`));
      console.warn('The app will run, but some API calls may fail.');
    }
    
    // Connect to MongoDB
    await connect();
    
    // Start server
    await startServer();
    
    app.isRunning = true;
    console.log('Trading Signals App initialized successfully');
    
    // Example usage
    try {
      console.log('Testing API clients...');
      
      // Test stocks API
      try {
        console.log("--- Testing stocks API ---");
        const stocksData = await getData('stocks', { type: 'stocks', symbol: 'AAPL', timeframe: 'D1' });
        console.log(`Stocks data received: ${stocksData.data?.length || 0} records`);
      } catch (e) {
        console.error(`Stocks API test failed: ${e.message}`);
      }
      
      // Test forex API
      try {
        console.log("--- Testing forex API ---");
        const forexData = await getData('forex', { type: 'forex', symbol: 'EURUSD', timeframe: 'H1' });
        console.log(`Forex data received: ${forexData.data?.length || 0} records`);
      } catch (e) {
        console.error(`Forex API test failed: ${e.message}`);
      }
      
      // Test commodities API
      try {
        console.log("--- Testing commodities API ---");
        const commoditiesData = await getData('commodities', { type: 'commodities', symbol: 'GOLD', timeframe: 'D1' });
        console.log(`Commodities data received: ${commoditiesData.data?.length || 0} records`);
      } catch (e) {
        console.error(`Commodities API test failed: ${e.message}`);
      }
      
      // Test economics API
      try {
        console.log("--- Testing economics API ---");
        const economicsData = await getData('economics', { type: 'economics', symbol: 'GDP', timeframe: 'M1' });
        console.log(`Economics data contains ${economicsData.observations?.length || 0} observations`);
      } catch (e) {
        console.error(`Economics API test failed: ${e.message}`);
      }
      
    } catch (error) {
      console.error('API test failed:', error);
      // Continue despite test failure
    }
  } catch (error) {
    console.error('Failed to initialize application:', error);
    process.exit(1);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('Application shutting down...');
  
  // Stop the server
  if (app.server) {
    app.server.kill();
  }
  
  // Clear all intervals
  Object.values(app.intervalIds).forEach(id => clearInterval(id));
  
  process.exit(0);
});

// Start the application
init(); 