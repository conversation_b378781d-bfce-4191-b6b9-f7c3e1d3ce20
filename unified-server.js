/**
 * Unified server script for Trading Signals App
 * Combines the functionality of main.js and server.js
 */

require('dotenv').config();
const express = require('express');
const path = require('path');
const cors = require('cors');
const morgan = require('morgan');
const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const mongoose = require('mongoose');
const { connect, findResults, saveResult, healthCheck: dbHealthCheck } = require('./db/mongo');
const config = require('./config');
const fallbackAPIs = require('./fallback/fallbackHandler');
const { calculateIndicators } = require('./utils/indicators');
const { generateSignals } = require('./utils/signals');
const { validateInputs } = require('./utils/validation');
const logger = require('./logging');
const cache = require('./cache');

// Define MongoDB Schema and Model
const ResultSchema = new mongoose.Schema({
  type: String,
  query: Object,
  result: Object,
  date: { type: Date, default: Date.now }
});

const Result = mongoose.model('Result', ResultSchema);

// Application state
const app = {
  server: null,
  isRunning: false
};

// Global error handling for uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('UNCAUGHT EXCEPTION:', error);
  logger.error('Stack trace:', error.stack);
  logger.warn('The application will continue running, but may be in an unstable state.');
});

// Global error handling for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('UNHANDLED PROMISE REJECTION:', reason);
  logger.warn('The application will continue running, but may be in an unstable state.');
});

/**
 * Validate required API keys
 * @returns {Object} - Object with validation results
 */
function validateApiKeys() {
  const keys = {
    ALPHA_VANTAGE_API_KEY: process.env.ALPHA_VANTAGE_API_KEY,
    TWELVE_DATA_API_KEY: process.env.TWELVE_DATA_API_KEY,
    FINNHUB_API_KEY: process.env.FINNHUB_API_KEY,
    FRED_API_KEY: process.env.FRED_API_KEY,
    POLYGON_API_KEY: process.env.POLYGON_API_KEY,
    OPENAI_API_KEY: process.env.OPENAI_API_KEY,
    FMP_API_KEY: process.env.FMP_API_KEY
  };

  const results = {
    valid: true,
    missing: [],
    messages: []
  };

  for (const [name, value] of Object.entries(keys)) {
    if (!value || value === 'demo' || value.trim() === '') {
      results.valid = false;
      results.missing.push(name);
      results.messages.push(`${name} is not set or using demo value. Get a valid API key for production use.`);
    }
  }

  return results;
}

/**
 * Get market data from API
 * @param {string} type - Market type (forex, stocks, etc)
 * @param {Object} query - Query parameters
 * @returns {Promise<Object>} - Market data
 */
async function getData(type, query) {
  logger.info(`Getting data for ${type}: ${JSON.stringify(query)}`);
  
  // Create a cache key
  const cacheKey = `market:${type}:${query.symbol}:${query.timeframe || 'D1'}`;
  
  try {
    // Check cache first
    const cachedData = await cache.get(cacheKey);
    if (cachedData) {
      logger.info(`Using cached data for ${type}:${query.symbol}`);
      return cachedData;
    }
    
    // Get data from API
    const apiOrder = config.API_PRIORITY[type];
    if (!apiOrder || !Array.isArray(apiOrder) || apiOrder.length === 0) {
      throw new Error(`No API clients configured for market type: ${type}`);
    }
    
    const result = await fallbackAPIs(apiOrder, query);
    
    // Check if result data is empty
    if (!result || (result.data && result.data.length === 0)) {
      logger.warn(`Warning: Empty data received for ${type}:${query.symbol}`);
    }
    
    // Store result in database using Mongoose model
    const newResult = new Result({
      type,
      query,
      result,
      date: new Date()
    });
    await newResult.save();
    
    // Cache the result - TTL based on timeframe
    let ttl = 300; // 5 minutes default
    if (query.timeframe) {
      // Adjust TTL based on timeframe
      const timeframeMap = {
        'M1': 60,         // 1 minute data - cache for 1 minute
        'M5': 60 * 5,     // 5 minute data - cache for 5 minutes
        'M15': 60 * 15,   // 15 minute data - cache for 15 minutes
        'M30': 60 * 30,   // 30 minute data - cache for 30 minutes
        'H1': 60 * 60,    // 1 hour data - cache for 1 hour
        'H4': 60 * 60 * 2, // 4 hour data - cache for 2 hours
        'D1': 60 * 60 * 6, // Daily data - cache for 6 hours
        'W1': 60 * 60 * 24, // Weekly data - cache for 24 hours
        'MN': 60 * 60 * 24 * 3 // Monthly data - cache for 3 days
      };
      ttl = timeframeMap[query.timeframe] || ttl;
    }
    
    await cache.set(cacheKey, result, ttl);
    
    return result;
  } catch (error) {
    logger.error(`Error getting data for ${type}:`, error);
    throw error;
  }
}

/**
 * Setup the Express server
 */
function setupServer() {
  const expressApp = express();
  const PORT = process.env.PORT || 3000;

  // Rate limiting
  const limiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100, // limit each IP to 100 requests per windowMs
    message: 'Too many requests from this IP, please try again later',
    standardHeaders: true,
    legacyHeaders: false,
  });

  // Advanced security middleware
  expressApp.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        scriptSrc: ["'self'", "'unsafe-inline'", "cdn.jsdelivr.net", "unpkg.com"],
        styleSrc: ["'self'", "'unsafe-inline'", "cdn.jsdelivr.net", "fonts.googleapis.com"],
        imgSrc: ["'self'", "data:", "cdn.jsdelivr.net"],
        connectSrc: ["'self'", "api.example.com", "*.alphavantage.co", "*.twelvedata.com", "*.finnhub.io", "*.fmp.io"],
        fontSrc: ["'self'", "fonts.gstatic.com", "cdn.jsdelivr.net"],
        objectSrc: ["'none'"],
        upgradeInsecureRequests: []
      }
    },
    crossOriginEmbedderPolicy: { policy: "credentialless" },
    crossOriginOpenerPolicy: { policy: "same-origin" },
    crossOriginResourcePolicy: { policy: "cross-origin" }
  }));

  // CORS configuration
  expressApp.use(cors({
    origin: process.env.CORS_ORIGIN ? process.env.CORS_ORIGIN.split(',') : '*',
    methods: ['GET', 'POST', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true,
    maxAge: 86400 // 24 hours
  }));

  // Middleware setup
  expressApp.use(compression({ level: 6 }));
  expressApp.use(morgan('combined', { stream: { write: message => logger.http(message.trim()) } }));
  expressApp.use(express.json({ limit: '10mb' }));
  expressApp.use(express.static(path.join(__dirname, 'public'), {
    maxAge: '1d',
    etag: true,
    lastModified: true
  }));
  expressApp.use('/api', limiter);

  // Advanced health check endpoint
  expressApp.get('/api/health', async (req, res) => {
    try {
      const [dbHealth, cacheHealth] = await Promise.all([
        dbHealthCheck(),
        cache.healthCheck()
      ]);
      
      res.json({
        status: 'ok',
        time: new Date(),
        services: {
          mongodb: dbHealth.status,
          cache: cacheHealth.status
        },
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        environment: process.env.NODE_ENV,
        version: process.env.npm_package_version
      });
    } catch (err) {
      logger.error('Health check failed:', err);
      res.status(503).json({ status: 'error', message: err.message });
    }
  });

  // Enhanced market data endpoint with caching and validation
  expressApp.get('/api/market/:type/:symbol', validateInputs, async (req, res) => {
    const { type, symbol } = req.params;
    const timeframe = req.query.timeframe || 'D1';

    try {
      const result = await getData(type.toLowerCase(), {
        type: type.toLowerCase(),
        symbol: symbol.toUpperCase(),
        timeframe
      });

      res.json(result);
    } catch (err) {
      logger.error('Market data fetch error:', err);
      res.status(503).json({
        error: 'Service temporarily unavailable',
        message: 'Unable to fetch market data',
        retryAfter: 60
      });
    }
  });

  // Advanced technical indicators endpoint
  expressApp.get('/api/indicators/:type/:symbol', validateInputs, async (req, res) => {
    try {
      const { type, symbol } = req.params;
      const timeframe = req.query.timeframe || 'D1';
      
      // First get market data
      const marketData = await getData(type.toLowerCase(), {
        type: type.toLowerCase(),
        symbol: symbol.toUpperCase(),
        timeframe
      });
      
      // Calculate indicators
      const indicators = calculateIndicators(marketData.data, req.query.indicators);
      
      res.json(indicators);
    } catch (err) {
      logger.error('Indicator calculation error:', err);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Unable to calculate indicators'
      });
    }
  });

  // Trading signals endpoint
  expressApp.get('/api/signals/:type/:symbol', validateInputs, async (req, res) => {
    try {
      const { type, symbol } = req.params;
      const timeframe = req.query.timeframe || 'D1';
      
      // First get market data
      const marketData = await getData(type.toLowerCase(), {
        type: type.toLowerCase(),
        symbol: symbol.toUpperCase(),
        timeframe
      });
      
      // Generate signals
      const signals = generateSignals(marketData.data);
      
      res.json(signals);
    } catch (err) {
      logger.error('Signal generation error:', err);
      res.status(500).json({
        error: 'Internal server error',
        message: 'Unable to generate signals'
      });
    }
  });

  // Catch-all route for SPA
  expressApp.get('*', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
  });

  // Start server
  app.server = expressApp.listen(PORT, () => {
    logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
  });

  return app.server;
}

/**
 * Initialize the application
 */
async function init() {
  logger.info('Initializing Trading Signals App...');
  
  // Validate API keys
  const apiKeyValidation = validateApiKeys();
  if (!apiKeyValidation.valid) {
    apiKeyValidation.messages.forEach(message => logger.warn(message));
  }
  
  try {
    // Connect to MongoDB
    await connect();
    
    // Setup and start server
    setupServer();
    
    app.isRunning = true;
    logger.info('Trading Signals App successfully initialized');
  } catch (error) {
    logger.error('Failed to initialize Trading Signals App:', error);
    process.exit(1);
  }
}

// Graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Received shutdown signal, closing connections...');
  
  if (app.server) {
    app.server.close(() => {
      logger.info('HTTP server closed');
    });
  }
  
  try {
    await mongoose.connection.close();
    logger.info('MongoDB connection closed');
    
    process.exit(0);
  } catch (err) {
    logger.error('Error during graceful shutdown:', err);
    process.exit(1);
  }
};

// Handle termination signals
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start the application
if (require.main === module) {
  init();
}

module.exports = {
  init,
  app,
  gracefulShutdown
};