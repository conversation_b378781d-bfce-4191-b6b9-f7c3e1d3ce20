/**
 * Minimal test server without Content Security Policy restrictions
 */

const express = require('express');
const path = require('path');
const app = express();
const PORT = process.env.PORT || 3000;

// Enable all static files in the public directory
app.use(express.static(path.join(__dirname, 'public')));

// Catch-all route to serve index.html for SPA
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start the server
app.listen(PORT, () => {
  console.log(`
=============================================
 TEST SERVER RUNNING
 
 Server: http://localhost:${PORT}
 
 No content security restrictions - use only for testing!
=============================================
  `);
}); 