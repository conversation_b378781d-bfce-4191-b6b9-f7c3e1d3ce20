<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Signals App</title>
    <meta name="theme-color" content="#2E3D63">
    <meta name="description" content="Professional trading signals application with technical analysis and real-time market data">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="TradingSignals">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- OpenGraph Tags for Social Media Sharing -->
    <meta property="og:title" content="Trading Signals App">
    <meta property="og:description" content="Professional trading signals application with technical analysis and real-time market data">
    <meta property="og:image" content="icon-512.png">
    <meta property="og:url" content="https://yourdomain.com/">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Trading Signals">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Trading Signals App">
    <meta name="twitter:description" content="Professional trading signals application with technical analysis and real-time market data">
    <meta name="twitter:image" content="icon-512.png">

    <!-- App Icons and Manifest -->
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="icon-192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="icon-192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="icon-512.png">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">
    <link rel="preload" href="css/styles.css" as="style">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="css/styles.css">
</head>
<body>
    <!-- Sticky Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #14213d;">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold text-warning" href="#">Trading Signals App</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                    <li class="nav-item"><a class="nav-link text-white" href="#market">Market</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="#chart">Chart</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="#calendar">Calendar</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="#signals">Signals</a></li>
                    <li class="nav-item"><a class="nav-link text-white" href="#backtesting">Backtesting</a></li>
                </ul>
                <button class="btn btn-warning ms-2" data-bs-toggle="modal" data-bs-target="#settingsModal">
                    <i class="fas fa-cog me-1"></i>Settings
                </button>
            </div>
        </div>
    </nav>
    <!-- Main Content -->
    <div class="container" style="margin-top: 80px;">
        <!-- Breadcrumb Navigation -->
        <nav aria-label="breadcrumb" id="main-breadcrumb" class="mt-3">
            <ol class="breadcrumb" style="background: #14213d; border-radius: 0.5rem;">
                <li class="breadcrumb-item"><a href="#market" class="text-warning">Market</a></li>
                <li class="breadcrumb-item"><a href="#chart" class="text-warning">Chart</a></li>
                <li class="breadcrumb-item"><a href="#calendar" class="text-warning">Calendar</a></li>
                <li class="breadcrumb-item"><a href="#signals" class="text-warning">Signals</a></li>
                <li class="breadcrumb-item"><a href="#backtesting" class="text-warning">Backtesting</a></li>
            </ol>
        </nav>
        <script>
        // Highlight current section in breadcrumb
        function updateBreadcrumb() {
            const hash = window.location.hash || '#market';
            document.querySelectorAll('#main-breadcrumb .breadcrumb-item').forEach(item => {
                const link = item.querySelector('a');
                if (link && link.getAttribute('href') === hash) {
                    link.classList.add('fw-bold');
                    link.style.color = '#fff';
                    item.style.background = '#fca311';
                    item.style.borderRadius = '0.5rem';
                } else if (link) {
                    link.classList.remove('fw-bold');
                    link.style.color = '#fca311';
                    item.style.background = 'none';
                }
            });
        }
        window.addEventListener('hashchange', updateBreadcrumb);
        window.addEventListener('DOMContentLoaded', updateBreadcrumb);
        </script>
        <header class="text-center my-4">
            <h1>Trading Signals Dashboard</h1>
            <p class="lead">Market Analysis and Trading Signals for Forex, Commodities, and Indices</p>
        </header>

        <!-- Market Selection & Overview Section -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>Market Selection</h2>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="realTimeDataToggle" checked>
                            <label class="form-check-label" for="realTimeDataToggle">Live Data</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="marketType" class="form-label">Market Type</label>
                            <select class="form-select" id="marketType">
                                <option value="forex">Forex (Currency Pairs)</option>
                                <option value="commodities">Commodities</option>
                                <option value="indices">Indices</option>
                                <option value="crypto">Cryptocurrencies</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="symbol" class="form-label">Symbol</label>
                            <select class="form-select" id="symbol">
                                <option value="EURUSD">EUR/USD</option>
                                <option value="GBPUSD">GBP/USD</option>
                                <option value="USDJPY">USD/JPY</option>
                                <option value="XAUUSD">Gold (XAU/USD)</option>
                                <option value="XAGUSD">Silver (XAG/USD)</option>
                                <option value="USOIL">US Oil</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="timeframe" class="form-label">Timeframe</label>
                            <select class="form-select" id="timeframe">
                                <option value="M5">5 Minutes</option>
                                <option value="M15">15 Minutes</option>
                                <option value="M30">30 Minutes</option>
                                <option value="H1">1 Hour</option>
                                <option value="H4">4 Hours</option>
                                <option value="D1">Daily</option>
                            </select>
                        </div>
                        <button id="analyzeBtn" class="btn btn-primary">Analyze Market</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>Market Overview</h2>
                        <button class="btn btn-sm btn-outline-primary" id="refreshMarketBtn">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="marketOverview">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h3 id="currentSymbol">EUR/USD</h3>
                                    <p id="currentPrice" class="fs-2 fw-bold">1.0875</p>
                                </div>
                                <div>
                                    <span id="dailyChange" class="badge bg-success fs-6">+0.25%</span>
                                    <div class="mt-2">
                                        <small class="text-muted">Last Update: <span id="lastUpdate">Now</span></small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h4>Market Trend</h4>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">65% Bullish</div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>Open:</span>
                                            <span id="dailyOpen" class="fw-bold">1.0860</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>High:</span>
                                            <span id="dailyHigh" class="fw-bold">1.0890</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>Low:</span>
                                            <span id="dailyLow" class="fw-bold">1.0845</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>Volume:</span>
                                            <span id="dailyVolume" class="fw-bold">12.5K</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Chart Area -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>Interactive Chart</h2>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" id="toggleIndicatorsBtn" data-bs-toggle="modal" data-bs-target="#indicatorsModal">
                                <i class="fas fa-chart-line me-1"></i>Indicators
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="togglePatternsBtn" data-bs-toggle="modal" data-bs-target="#patternsModal">
                                <i class="fas fa-shapes me-1"></i>Patterns
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="toggleSmcBtn" data-bs-toggle="modal" data-bs-target="#smcModal">
                                <i class="fas fa-layer-group me-1"></i>SMC/ICT
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="chartContainer" style="position: relative; height: 400px;">
                            <canvas id="priceChart" width="400" height="400"></canvas>
                        </div>
                        <div class="d-flex justify-content-center mt-3">
                            <div class="btn-group" role="group" aria-label="Chart Type">
                                <button type="button" class="btn btn-outline-primary active" id="candlestickBtn">Candlestick</button>
                                <button type="button" class="btn btn-outline-primary" id="lineChartBtn">Line</button>
                                <button type="button" class="btn btn-outline-primary" id="areaChartBtn">Area</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>Economic Calendar</h2>
                        <button class="btn btn-sm btn-outline-primary" id="refreshCalendarBtn">
                            <i class="fas fa-sync-alt me-1"></i>Refresh
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 id="calendarDate" class="mb-0">Wednesday, July 5, 2023</h5>
                            <div>
                                <select class="form-select form-select-sm" id="calendarFilter">
                                    <option value="all">All Events</option>
                                    <option value="high">High Impact Only</option>
                                    <option value="medium">Medium Impact Only</option>
                                    <option value="low">Low Impact Only</option>
                                </select>
                            </div>
                        </div>
                        <div id="calendarStatus"></div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Currency</th>
                                        <th>Event</th>
                                        <th>Impact</th>
                                        <th>Forecast</th>
                                    </tr>
                                </thead>
                                <tbody id="calendarBody">
                                    <!-- Economic events will be loaded here -->
                                    <tr data-importance="high" class="table-danger">
                                        <td>08:30</td>
                                        <td>USD</td>
                                        <td>Non-Farm Payrolls</td>
                                        <td><span class="badge bg-danger">High</span></td>
                                        <td>200K</td>
                                    </tr>
                                    <tr data-importance="high" class="table-danger">
                                        <td>10:00</td>
                                        <td>EUR</td>
                                        <td>ECB Interest Rate Decision</td>
                                        <td><span class="badge bg-danger">High</span></td>
                                        <td>4.50%</td>
                                    </tr>
                                    <tr data-importance="medium" class="table-warning">
                                        <td>14:00</td>
                                        <td>USD</td>
                                        <td>ISM Manufacturing Index</td>
                                        <td><span class="badge bg-warning text-dark">Medium</span></td>
                                        <td>49.8</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trading Signals Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>Trading Signals</h2>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" id="signalHistoryBtn" data-bs-toggle="modal" data-bs-target="#signalHistoryModal">
                                <i class="fas fa-history me-1"></i>Signal History
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="exportSignalsBtn">
                                <i class="fas fa-file-export me-1"></i>Export
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="importSignalsBtn" data-bs-toggle="modal" data-bs-target="#importSignalsModal">
                                <i class="fas fa-file-import me-1"></i>Import
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs mb-3" id="signalsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="current-signals-tab" data-bs-toggle="tab" data-bs-target="#current-signals" type="button" role="tab" aria-controls="current-signals" aria-selected="true">Current Signals</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="signal-analytics-tab" data-bs-toggle="tab" data-bs-target="#signal-analytics" type="button" role="tab" aria-controls="signal-analytics" aria-selected="false">Performance Analytics</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="signalsTabContent">
                            <div class="tab-pane fade show active" id="current-signals" role="tabpanel" aria-labelledby="current-signals-tab">
                                <div id="signalsContainer">
                                    <div class="alert alert-success">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <h4>Buy Signal</h4>
                                            <div>
                                                <span class="badge bg-info">EUR/USD</span>
                                                <span class="badge bg-secondary">H1</span>
                                                <button class="btn btn-sm btn-outline-success ms-2" data-signal-id="1" title="Track Signal">
                                                    <i class="fas fa-plus-circle"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <p><strong>Entry Point:</strong> 1.0880</p>
                                        <p><strong>Stop Loss:</strong> 1.0850 <span class="text-muted">(30 pips)</span></p>
                                        <p><strong>Take Profit:</strong> 1.0930 <span class="text-muted">(50 pips)</span></p>
                                        <p><strong>Risk/Reward Ratio:</strong> <span class="badge bg-success">1:1.67</span></p>
                                        <p><strong>Analysis:</strong> Price broke above the 200 MA with increasing volume. RSI shows bullish momentum at 65.</p>
                                        <div class="progress mt-2" style="height: 5px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" title="Signal Confidence: 75%"></div>
                                        </div>
                                        <small class="text-muted">Created: Today 10:30 AM</small>
                                    </div>

                                    <div class="alert alert-danger mt-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <h4>Sell Signal (Alternative Scenario)</h4>
                                            <div>
                                                <span class="badge bg-info">EUR/USD</span>
                                                <span class="badge bg-secondary">H1</span>
                                                <button class="btn btn-sm btn-outline-success ms-2" data-signal-id="2" title="Track Signal">
                                                    <i class="fas fa-plus-circle"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <p><strong>Entry Point:</strong> 1.0840</p>
                                        <p><strong>Stop Loss:</strong> 1.0870 <span class="text-muted">(30 pips)</span></p>
                                        <p><strong>Take Profit:</strong> 1.0790 <span class="text-muted">(50 pips)</span></p>
                                        <p><strong>Risk/Reward Ratio:</strong> <span class="badge bg-success">1:1.67</span></p>
                                        <p><strong>Analysis:</strong> If price fails to hold the 1.0850 support, expect a reversal. MACD shows possible bearish crossover.</p>
                                        <div class="progress mt-2" style="height: 5px;">
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 45%;" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" title="Signal Confidence: 45%"></div>
                                        </div>
                                        <small class="text-muted">Created: Today 10:35 AM</small>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="signal-analytics" role="tabpanel" aria-labelledby="signal-analytics-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">Win Rate</h5>
                                                <div class="display-4 fw-bold text-success">68%</div>
                                                <p class="text-muted">Last 30 Signals</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">Avg. Profit/Loss</h5>
                                                <div class="display-4 fw-bold text-success">1.8</div>
                                                <p class="text-muted">Profit:Loss Ratio</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h5 class="card-title">Signal Performance by Pair</h5>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>Pair</th>
                                                        <th>Signals</th>
                                                        <th>Win Rate</th>
                                                        <th>Avg. Pips</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>EUR/USD</td>
                                                        <td>24</td>
                                                        <td>75%</td>
                                                        <td>+42</td>
                                                    </tr>
                                                    <tr>
                                                        <td>GBP/USD</td>
                                                        <td>18</td>
                                                        <td>67%</td>
                                                        <td>+38</td>
                                                    </tr>
                                                    <tr>
                                                        <td>USD/JPY</td>
                                                        <td>15</td>
                                                        <td>60%</td>
                                                        <td>+25</td>
                                                    </tr>
                                                    <tr>
                                                        <td>Gold</td>
                                                        <td>12</td>
                                                        <td>83%</td>
                                                        <td>+68</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">Signal Performance by Type</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title">Buy Signals</h6>
                                                        <div class="text-center">
                                                            <div class="display-6 fw-bold text-success">72%</div>
                                                            <p class="text-muted">Win Rate (36 signals)</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title">Sell Signals</h6>
                                                        <div class="text-center">
                                                            <div class="display-6 fw-bold text-success">65%</div>
                                                            <p class="text-muted">Win Rate (28 signals)</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backtesting Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>Strategy Backtesting</h2>
                        <button class="btn btn-sm btn-outline-primary" id="saveStrategyBtn">
                            <i class="fas fa-save me-1"></i>Save Strategy
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Backtest Settings</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="backtestForm">
                                            <div class="mb-3">
                                                <label for="backtestSymbol" class="form-label">Pair</label>
                                                <select class="form-select" id="backtestSymbol">
                                                    <option value="EURUSD">EUR/USD</option>
                                                    <option value="GBPUSD">GBP/USD</option>
                                                    <option value="USDJPY">USD/JPY</option>
                                                    <option value="XAUUSD">Gold (XAU/USD)</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="backtestTimeframe" class="form-label">Timeframe</label>
                                                <select class="form-select" id="backtestTimeframe">
                                                    <option value="M15">15 Minutes</option>
                                                    <option value="H1" selected>1 Hour</option>
                                                    <option value="H4">4 Hours</option>
                                                    <option value="D1">Daily</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="backtestPeriod" class="form-label">Backtest Period</label>
                                                <select class="form-select" id="backtestPeriod">
                                                    <option value="1M">1 Month</option>
                                                    <option value="3M" selected>3 Months</option>
                                                    <option value="6M">6 Months</option>
                                                    <option value="1Y">1 Year</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="backtestStrategy" class="form-label">Strategy</label>
                                                <select class="form-select" id="backtestStrategy">
                                                    <option value="macd_cross">MACD Cross</option>
                                                    <option value="rsi_overbought">RSI Overbought/Oversold</option>
                                                    <option value="ma_cross">MA Cross</option>
                                                    <option value="breakout">Breakout</option>
                                                    <option value="custom">Custom Strategy</option>
                                                </select>
                                            </div>
                                            <div id="strategyParams" class="mb-3">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title">Strategy Parameters</h6>
                                                        <div class="mb-2">
                                                            <label for="param1" class="form-label">MACD (Fast)</label>
                                                            <input type="number" class="form-control form-control-sm" id="param1" value="12">
                                                        </div>
                                                        <div class="mb-2">
                                                            <label for="param2" class="form-label">MACD (Slow)</label>
                                                            <input type="number" class="form-control form-control-sm" id="param2" value="26">
                                                        </div>
                                                        <div class="mb-2">
                                                            <label for="param3" class="form-label">MACD (Signal)</label>
                                                            <input type="number" class="form-control form-control-sm" id="param3" value="9">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="riskPerTrade" class="form-label">Risk per Trade</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="riskPerTrade" value="2">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                            <button type="button" id="runBacktestBtn" class="btn btn-primary w-100">
                                                <i class="fas fa-play me-1"></i>Run Backtest
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">Backtest Results</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="backtestResults">
                                            <div class="row mb-3">
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">Net Profit</h6>
                                                            <div class="text-success fw-bold">+12.8%</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">Win Rate</h6>
                                                            <div class="fw-bold">68%</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">Trades</h6>
                                                            <div class="fw-bold">42</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">Sharpe Ratio</h6>
                                                            <div class="fw-bold">1.85</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-12">
                                                    <div class="card">
                                                        <div class="card-body p-2">
                                                            <canvas id="equityCurve" height="200"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="card">
                                                        <div class="card-header py-2 bg-light">
                                                            <h6 class="mb-0">Trade Stats</h6>
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <table class="table table-sm mb-0">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>Avg. Profit</td>
                                                                        <td class="text-end text-success">+1.8%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Avg. Loss</td>
                                                                        <td class="text-end text-danger">-0.9%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Profit:Loss Ratio</td>
                                                                        <td class="text-end">2.0</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Max Drawdown</td>
                                                                        <td class="text-end text-danger">-5.2%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>Avg. Trade Duration</td>
                                                                        <td class="text-end">2.4 days</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card">
                                                        <div class="card-header py-2 bg-light">
                                                            <h6 class="mb-0">Trade Distribution</h6>
                                                        </div>
                                                        <div class="card-body p-2">
                                                            <canvas id="tradesDistribution" height="150"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Indicators & Support/Resistance Section -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h2>Technical Indicators</h2>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>Indicator</th>
                                    <th>Value</th>
                                    <th>Signal</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>RSI (14)</td>
                                    <td>65.3</td>
                                    <td><span class="badge bg-success">Buy</span></td>
                                </tr>
                                <tr>
                                    <td>MACD (12,26,9)</td>
                                    <td>0.0023</td>
                                    <td><span class="badge bg-success">Buy</span></td>
                                </tr>
                                <tr>
                                    <td>MA (50)</td>
                                    <td>1.0840</td>
                                    <td><span class="badge bg-success">Buy</span></td>
                                </tr>
                                <tr>
                                    <td>MA (200)</td>
                                    <td>1.0820</td>
                                    <td><span class="badge bg-success">Buy</span></td>
                                </tr>
                                <tr>
                                    <td>Bollinger Bands</td>
                                    <td>Upper: 1.0910</td>
                                    <td><span class="badge bg-warning text-dark">Neutral</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h2>Support & Resistance Levels</h2>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h4>Resistance Levels</h4>
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-danger">R3: 1.0950</span>
                                <span class="badge bg-danger">R2: 1.0930</span>
                                <span class="badge bg-danger">R1: 1.0910</span>
                            </div>
                        </div>
                        <div>
                            <h4>Support Levels</h4>
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-primary">S1: 1.0850</span>
                                <span class="badge bg-primary">S2: 1.0830</span>
                                <span class="badge bg-primary">S3: 1.0810</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4>Key Price Levels</h4>
                            <p><strong>Daily Pivot:</strong> 1.0865</p>
                            <p><strong>Weekly High:</strong> 1.0925</p>
                            <p><strong>Weekly Low:</strong> 1.0805</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel"><i class="fas fa-cog me-2"></i>Settings</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <!-- Theme Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">Theme Settings</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="darkModeToggle">
                            <label class="form-check-label" for="darkModeToggle">Dark Mode</label>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">Notification Settings</h6>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="notificationToggle">
                            <label class="form-check-label" for="notificationToggle">
                                Popup Notifications
                                <span id="notificationStatus" class="me-2 badge bg-secondary">Not Set</span>
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="signalNotifications" checked>
                            <label class="form-check-label" for="signalNotifications">
                                Trading Signal Alerts
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="economicNotifications" checked>
                            <label class="form-check-label" for="economicNotifications">
                                Economic Calendar Alerts
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="priceNotifications" checked>
                            <label class="form-check-label" for="priceNotifications">
                                Price Alerts
                            </label>
                        </div>
                    </div>

                    <!-- Voice Command Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">Voice Command Settings</h6>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="voiceCommandsToggle">
                            <label class="form-check-label" for="voiceCommandsToggle">
                                Enable Voice Commands
                                <span id="voiceCommandsStatus" class="me-2 badge bg-secondary">Not Set</span>
                            </label>
                        </div>
                        <div class="mt-3">
                            <p class="small text-muted">Available voice commands:</p>
                            <ul class="small text-muted ps-3">
                                <li>Show chart</li>
                                <li>Change timeframe to [5 minutes, 15 minutes, ...]</li>
                                <li>Change symbol to [EUR, GBP, Gold, ...]</li>
                                <li>Analyze market</li>
                                <li>Toggle theme</li>
                                <li>Refresh data</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Language Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">Language Settings</h6>
                        <div class="mb-3">
                            <label for="languageSelect" class="form-label">Interface Language</label>
                            <select class="form-select" id="languageSelect">
                                <option value="en" selected>English</option>
                                <option value="ar">Arabic (العربية)</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                    <button type="button" class="btn btn-primary" id="saveSettingsBtn">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts (optimized and cleaned) -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.3.0/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.3.1/dist/chartjs-adapter-luxon.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial@0.1.1/dist/chartjs-chart-financial.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <script src="js/api-key-manager.js"></script>
    <script src="js/market-data-service.js"></script>
    <script src="js/technical-analysis.js"></script>
    <script src="js/chart.js"></script>
    <script src="js/chart-fix.js"></script>
    <script src="js/app.js"></script>
    <script src="register-sw.min.js" type="module"></script>
    <!-- Fallback Content Handler and Google Analytics (unchanged) -->
    <script>
        // ... existing code ...
    </script>
</body>
</html>

