{"name": "trading-signals-app", "version": "1.2.0", "description": "An advanced collaborative web application for market analysis and trading signals", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "debug": "node --inspect server.js", "test": "jest --coverage", "lint": "eslint . --fix", "build": "webpack --mode production", "analyze": "webpack-bundle-analyzer stats.json", "prepare": "husky install", "install-deps": "npm install", "health-check": "curl http://localhost:3000/health"}, "keywords": ["trading", "financial-markets", "technical-analysis", "signals", "charts", "real-time", "collaboration", "analytics"], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.7", "bcrypt": "^5.1.0", "bull": "^4.11.3", "chart.js": "^4.3.0", "compression": "^1.8.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "helmet": "^7.2.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.0", "mongodb": "^6.16.0", "mongoose": "^8.15.0", "morgan": "^1.10.0", "node-cache": "^5.1.2", "socket.io": "^4.7.2", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"eslint": "^8.40.0", "husky": "^8.0.3", "jest": "^29.5.0", "lint-staged": "^15.2.0", "nodemon": "^2.0.22", "prettier": "^3.1.1", "webpack": "^5.89.0", "webpack-bundle-analyzer": "^4.10.1", "webpack-cli": "^5.1.4"}, "engines": {"node": ">=16.0.0"}, "lint-staged": {"*.js": "eslint --fix", "*.{js,json,md}": "prettier --write"}}