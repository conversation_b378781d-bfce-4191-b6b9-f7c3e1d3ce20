/**
 * Cleanup script for the Trading Signals App
 * Run this script to organize the project structure
 */

const fs = require('fs');
const path = require('path');

// Files to archive (move to archived_files directory)
const filesToArchive = [
  'test-server.js',
  'check-server.js',
  'server.js',
  'check-main-page.js',
  'create-server.js',
  'main.js',
  'package.json.backup'
];

// Ensure archived_files directory exists
if (!fs.existsSync('archived_files')) {
  fs.mkdirSync('archived_files', { recursive: true });
  console.log('✅ Created archived_files directory');
}

// Archive files
let archivedCount = 0;
filesToArchive.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      fs.renameSync(file, path.join('archived_files', file));
      console.log(`✅ Archived: ${file}`);
      archivedCount++;
    }
  } catch (error) {
    console.error(`❌ Error archiving ${file}:`, error.message);
  }
});

// Ensure required directories exist
const requiredDirs = [
  'public',
  'logs',
  'db',
  'config',
  'fallback',
  'api_clients'
];

let createdDirCount = 0;
requiredDirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
    createdDirCount++;
  }
});

// Clean up any invalid files
const invalidFiles = ['{'];
let removedCount = 0;
invalidFiles.forEach(file => {
  try {
    if (fs.existsSync(file)) {
      fs.unlinkSync(file);
      console.log(`✅ Removed invalid file: ${file}`);
      removedCount++;
    }
  } catch (error) {
    console.error(`❌ Error removing ${file}:`, error.message);
  }
});

console.log('\n==== Cleanup Summary ====');
console.log(`Files archived: ${archivedCount}`);
console.log(`Directories created: ${createdDirCount}`);
console.log(`Invalid files removed: ${removedCount}`);
console.log('\nTo verify the application is working correctly, run:');
console.log('1. npm install');
console.log('2. node db-check.js');
console.log('3. npm start');
console.log('\nThen visit: http://localhost:3000'); 