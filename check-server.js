/**
 * Server check and repair utility for Trading Signals App
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

// Configuration
const PORT = process.env.PORT || 3000;
const HOST = 'localhost';

console.log('Trading Signals App - Server Check Utility');
console.log('=========================================');

// Check if public folder exists and has files
const checkPublicFolder = () => {
  const publicPath = path.join(__dirname, 'public');
  
  console.log(`Checking public folder at ${publicPath}...`);
  
  if (!fs.existsSync(publicPath)) {
    console.error('❌ Error: public folder does not exist!');
    console.log('Creating public folder...');
    fs.mkdirSync(publicPath, { recursive: true });
    return false;
  }
  
  const files = fs.readdirSync(publicPath);
  console.log(`Found ${files.length} files in public folder.`);
  
  if (files.length === 0) {
    console.error('❌ Error: public folder is empty!');
    return false;
  }
  
  if (!files.includes('index.html')) {
    console.error('❌ Error: index.html not found in public folder!');
    return false;
  }
  
  console.log('✅ Public folder check passed.');
  return true;
};

// Check if server is running
const checkServerRunning = () => {
  return new Promise((resolve) => {
    const req = http.request({
      hostname: HOST,
      port: PORT,
      path: '/',
      method: 'GET',
      timeout: 3000 // 3 second timeout
    }, (res) => {
      console.log(`✅ Server is running on http://${HOST}:${PORT}/ (Status: ${res.statusCode})`);
      resolve(true);
    });
    
    req.on('error', (err) => {
      console.error(`❌ Server is not running: ${err.message}`);
      resolve(false);
    });
    
    req.on('timeout', () => {
      console.error('❌ Server request timed out');
      req.destroy();
      resolve(false);
    });
    
    req.end();
  });
};

// Check dependencies in package.json
const checkDependencies = () => {
  try {
    const packageJsonPath = path.join(__dirname, 'package.json');
    const packageData = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    
    console.log('Checking required dependencies...');
    
    const requiredDeps = [
      'express', 
      'winston', 
      'winston-daily-rotate-file',
      'cors',
      'compression',
      'helmet',
      'mongodb'
    ];
    
    const missingDeps = requiredDeps.filter(dep => 
      !packageData.dependencies || !packageData.dependencies[dep]
    );
    
    if (missingDeps.length > 0) {
      console.error(`❌ Missing dependencies: ${missingDeps.join(', ')}`);
      return missingDeps;
    }
    
    console.log('✅ All required dependencies are listed in package.json');
    return [];
  } catch (err) {
    console.error('❌ Error checking dependencies:', err.message);
    return [];
  }
};

// Install missing dependencies
const installMissingDependencies = (missingDeps) => {
  if (missingDeps.length === 0) return Promise.resolve();
  
  return new Promise((resolve, reject) => {
    console.log(`Installing missing dependencies: ${missingDeps.join(', ')}...`);
    
    const command = `npm install --save ${missingDeps.join(' ')}`;
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`❌ Error installing dependencies: ${error.message}`);
        reject(error);
        return;
      }
      
      console.log('✅ Dependencies installed successfully');
      resolve();
    });
  });
};

// Create basic index.html if missing
const createBasicIndexHtml = () => {
  const publicPath = path.join(__dirname, 'public');
  const indexPath = path.join(publicPath, 'index.html');
  
  if (fs.existsSync(indexPath)) {
    return;
  }
  
  console.log('Creating basic index.html file...');
  
  const basicHtml = `<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Trading Signals App</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: white;
      padding: 20px;
      border-radius: 5px;
      box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }
    h1 {
      color: #2c3e50;
    }
    .status {
      padding: 15px;
      margin: 10px 0;
      border-radius: 4px;
    }
    .success {
      background-color: #d4edda;
      color: #155724;
    }
    .error {
      background-color: #f8d7da;
      color: #721c24;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>Trading Signals App</h1>
    <p>Welcome to the Trading Signals App! This is a basic index.html file created by the server check utility.</p>
    <div class="status success">
      <h3>Server Status</h3>
      <p>The server is running correctly! If you're seeing this page, it means the server is able to serve static files.</p>
    </div>
    <hr>
    <h2>Next Steps</h2>
    <ul>
      <li>Check the server console for any errors</li>
      <li>Add your own content to the public folder</li>
      <li>Configure API endpoints in unified-server.js</li>
    </ul>
  </div>
</body>
</html>`;
  
  fs.writeFileSync(indexPath, basicHtml);
  console.log('✅ Basic index.html file created');
};

// Main function
const main = async () => {
  // Check public folder
  const publicFolderOk = checkPublicFolder();
  
  if (!publicFolderOk) {
    createBasicIndexHtml();
  }
  
  // Check dependencies
  const missingDeps = checkDependencies();
  
  if (missingDeps.length > 0) {
    try {
      await installMissingDependencies(missingDeps);
    } catch (err) {
      // Continue even if installation fails
    }
  }
  
  // Check if server is running
  const serverRunning = await checkServerRunning();
  
  if (!serverRunning) {
    console.log('\nRecommendations:');
    console.log('1. Run "npm install" to ensure all dependencies are installed');
    console.log('2. Make sure port 3000 is not already in use');
    console.log('3. Try starting the server with "node unified-server.js"');
  }
  
  console.log('\nCheck completed.');
};

// Run the main function
main(); 