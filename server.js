require('dotenv').config();
const express = require('express');
const path = require('path');
const cors = require('cors');
const morgan = require('morgan');
const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
require('winston-daily-rotate-file');
const Redis = require('ioredis');
const { connect, findResults, saveResult } = require('./db/mongo');
const config = require('./config');
const fallbackAPIs = require('./fallback/fallbackHandler');
const { calculateIndicators } = require('./utils/indicators');
const { generateSignals } = require('./utils/signals');
const { validateInputs } = require('./utils/validation');

// Configure Redis
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  retryStrategy: (times) => Math.min(times * 50, 2000)
});

// Configure Winston logger with daily rotate
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxFiles: '14d'
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxFiles: '14d'
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

const app = express();
const PORT = process.env.PORT || 3000;

// Setup security and performance middleware with configurations from config file
app.use(helmet(config.APP_SETTINGS.security.helmet));
app.use(compression()); // Compress responses
app.use(express.json()); // Parse JSON bodies
app.use(express.urlencoded({ extended: true })); // Parse URL-encoded bodies

// Configure CORS using settings from config
app.use(cors({
  origin: config.APP_SETTINGS.security.cors.allowedOrigins,
  methods: config.APP_SETTINGS.security.cors.allowedMethods,
  allowedHeaders: config.APP_SETTINGS.security.cors.allowedHeaders,
  credentials: true,
  maxAge: config.APP_SETTINGS.security.cors.maxAge
}));

// Set up request logging
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.http(message.trim())
  }
}));

// Serve static files
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));

// Add rate limiting for all requests using config
const apiLimiter = rateLimit({
  windowMs: config.APP_SETTINGS.rateLimiting.windowMs,
  max: config.APP_SETTINGS.rateLimiting.maxRequestsPerWindow,
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    status: 429,
    message: 'Too many requests, please try again later.'
  }
});
app.use('/api/', apiLimiter);

// Graceful MongoDB connection with exponential backoff
const connectWithRetry = async (retries = 5, initialDelay = 1000) => {
  for (let i = 0; i < retries; i++) {
    try {
      await connect();
      logger.info('Successfully connected to MongoDB');
      return;
    } catch (err) {
      const delay = initialDelay * Math.pow(2, i);
      logger.error(`MongoDB connection attempt ${i + 1}/${retries} failed:`, err);
      if (i < retries - 1) await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  throw new Error('Failed to connect to MongoDB after multiple attempts');
};

// Initialize services
const initializeServices = async () => {
  try {
    await connectWithRetry();
    await redis.ping();
    logger.info('Redis connection established');
  } catch (err) {
    logger.error('Service initialization failed:', err);
    process.exit(1);
  }
};

initializeServices();

// Health check endpoint
app.get('/health', async (req, res) => {
  try {
    const dbStatus = await mongoose.connection.db.admin().ping();
    const redisStatus = await redis.ping();
    
    res.json({
      status: 'ok',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      database: dbStatus ? 'connected' : 'disconnected',
      cache: redisStatus === 'PONG' ? 'connected' : 'disconnected',
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version
    });
  } catch (error) {
    logger.error('Health check failed:', error);
    res.status(500).json({
      status: 'error',
      message: 'Health check failed'
    });
  }
});

// API Routes
const apiRouter = express.Router();

// Market Data endpoint - with caching
apiRouter.get('/market/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;
  const limit = parseInt(req.query.limit || '100', 10);
  
  try {
    // Use cache wrapper to automatically handle cache read/write
    const cacheKey = `market_${symbol}_${timeframe}_${limit}`;
    const cacheTTL = config.CACHE_TTL_BY_TIMEFRAME[timeframe] || config.CACHE_SETTINGS.redis.defaultTTL;
    
    const data = await cache.cacheWrapper(
      cacheKey,
      async () => {
        // Fetch from database if not in cache
        const marketData = await mongoose.connection.db.collection('marketData').find({
          symbol: symbol.toUpperCase(),
          timeframe
        })
        .sort({ timestamp: -1 })
        .limit(limit)
        .toArray();
        
        return marketData;
      },
      cacheTTL
    );
    
    res.json({
      status: 'success',
      data
    });
  } catch (error) {
    logger.error(`Error fetching market data: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to fetch market data'
    });
  }
});

// Generate indicators endpoint
apiRouter.get('/indicators/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;
  const { indicators: requestedIndicators } = req.query;
  
  try {
    // Fetch market data
    const marketData = await mongoose.connection.db.collection('marketData').find({
      symbol: symbol.toUpperCase(),
      timeframe
    })
    .sort({ timestamp: -1 })
    .limit(config.APP_SETTINGS.maxDataPoints)
    .toArray();
    
    if (!marketData || marketData.length === 0) {
      return res.status(404).json({
        status: 'error',
        message: 'No market data found for the specified symbol and timeframe'
      });
    }
    
    // Process indicators
    const result = calculateIndicators(marketData, requestedIndicators);
    
    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error(`Error calculating indicators: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to calculate indicators'
    });
  }
});

// Generate trading signals endpoint
apiRouter.get('/signals/:symbol/:timeframe', validateInputs, async (req, res) => {
  const { symbol, timeframe } = req.params;
  
  try {
    // Use cache for signals with a shorter TTL than market data
    const cacheKey = `signals_${symbol}_${timeframe}`;
    const cacheTTL = Math.min(config.CACHE_TTL_BY_TIMEFRAME[timeframe] / 2 || 1800, 1800); // Half of market data TTL or 30 min
    
    const result = await cache.cacheWrapper(
      cacheKey,
      async () => {
        // Fetch market data
        const marketData = await mongoose.connection.db.collection('marketData').find({
          symbol: symbol.toUpperCase(),
          timeframe
        })
        .sort({ timestamp: -1 })
        .limit(config.APP_SETTINGS.maxDataPoints)
        .toArray();
        
        if (!marketData || marketData.length === 0) {
          return null; // Will be handled below
        }
        
        // Generate signals
        return generateSignals(marketData);
      },
      cacheTTL
    );
    
    if (!result) {
      return res.status(404).json({
        status: 'error',
        message: 'No market data found for the specified symbol and timeframe'
      });
    }
    
    res.json({
      status: 'success',
      data: result
    });
  } catch (error) {
    logger.error(`Error generating signals: ${error.message}`);
    res.status(500).json({
      status: 'error',
      message: 'Failed to generate trading signals'
    });
  }
});

// Mount API router
app.use('/api', apiRouter);

// Serve static files for the frontend
app.use(express.static(path.join(__dirname, 'public')));

// Handle SPA routing
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Start the server
const server = app.listen(PORT, () => {
  logger.info(`Server running on port ${PORT} in ${process.env.NODE_ENV || 'development'} mode`);
  logger.info(`Health check available at http://localhost:${PORT}/health`);
});

// Handle graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Received shutdown signal, shutting down gracefully');
  
  server.close(() => {
    logger.info('HTTP server closed');
  });
  
  try {
    await mongoose.connection.close();
    logger.info('MongoDB connection closed');
    
    await redis.quit();
    logger.info('Redis connection closed');
    
    process.exit(0);
  } catch (err) {
    logger.error('Error during shutdown:', err);
    process.exit(1);
  }
};

// Handle termination signals
process.on('SIGINT', gracefulShutdown);
process.on('SIGTERM', gracefulShutdown);

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error(`Uncaught Exception: ${err.message}\nStack: ${err.stack}`);
  process.exit(1);
});

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Promise Rejection:');
  logger.error(reason);
});

module.exports = app; // Export for testing