<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Signals App</title>
    <meta name="theme-color" content="#2E3D63">
    <meta name="description" content="Professional trading signals application with technical analysis and real-time market data">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="TradingSignals">
    <meta name="mobile-web-app-capable" content="yes">

    <!-- OpenGraph Tags for Social Media Sharing -->
    <meta property="og:title" content="Trading Signals App">
    <meta property="og:description" content="Professional trading signals application with technical analysis and real-time market data">
    <meta property="og:image" content="icon-512.png">
    <meta property="og:url" content="https://yourdomain.com/">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Trading Signals">

    <!-- Twitter Card Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="Trading Signals App">
    <meta name="twitter:description" content="Professional trading signals application with technical analysis and real-time market data">
    <meta name="twitter:image" content="icon-512.png">

    <!-- App Icons and Manifest -->
    <link rel="manifest" href="manifest.json">
    <link rel="apple-touch-icon" href="icon-192.png">
    <link rel="icon" type="image/png" sizes="192x192" href="icon-192.png">
    <link rel="icon" type="image/png" sizes="512x512" href="icon-512.png">
    <link rel="preconnect" href="https://cdn.jsdelivr.net">
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" as="style">
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style">
    <link rel="preload" href="src/styles/styles.css" as="style">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <link rel="stylesheet" href="src/styles/styles.css">
</head>
<body>
    <div class="container">
        <header class="text-center my-4">
            <h1>Trading Signals Dashboard</h1>
            <p class="lead">Market Analysis and Trading Signals for Forex, Commodities and Indices</p>
        </header>

        <!-- Market Selection & Overview Section -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>اختيار السوق</h2>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="realTimeDataToggle" checked>
                            <label class="form-check-label" for="realTimeDataToggle">بيانات حية</label>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="marketType" class="form-label">نوع السوق</label>
                            <select class="form-select" id="marketType">
                                <option value="forex">العملات (أزواج العملات)</option>
                                <option value="commodities">السلع</option>
                                <option value="indices">المؤشرات</option>
                                <option value="crypto">العملات الرقمية</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="symbol" class="form-label">الرمز</label>
                            <select class="form-select" id="symbol">
                                <option value="EURUSD">يورو/دولار (EUR/USD)</option>
                                <option value="GBPUSD">جنيه/دولار (GBP/USD)</option>
                                <option value="USDJPY">دولار/ين (USD/JPY)</option>
                                <option value="XAUUSD">الذهب (XAU/USD)</option>
                                <option value="XAGUSD">الفضة (XAG/USD)</option>
                                <option value="USOIL">النفط الأمريكي</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label for="timeframe" class="form-label">الإطار الزمني</label>
                            <select class="form-select" id="timeframe">
                                <option value="M5">5 دقائق</option>
                                <option value="M15">15 دقيقة</option>
                                <option value="M30">30 دقيقة</option>
                                <option value="H1">ساعة</option>
                                <option value="H4">4 ساعات</option>
                                <option value="D1">يومي</option>
                            </select>
                        </div>
                        <button id="analyzeBtn" class="btn btn-primary">تحليل السوق</button>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>نظرة عامة على السوق</h2>
                        <button class="btn btn-sm btn-outline-primary" id="refreshMarketBtn">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="marketOverview">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <h3 id="currentSymbol">يورو/دولار</h3>
                                    <p id="currentPrice" class="fs-2 fw-bold">1.0875</p>
                                </div>
                                <div>
                                    <span id="dailyChange" class="badge bg-success fs-6">+0.25%</span>
                                    <div class="mt-2">
                                        <small class="text-muted">آخر تحديث: <span id="lastUpdate">الآن</span></small>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <h4>اتجاه السوق</h4>
                                <div class="progress">
                                    <div class="progress-bar bg-success" role="progressbar" style="width: 65%;" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100">65% صاعد</div>
                                </div>
                            </div>
                            <div class="mt-3">
                                <div class="row">
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>افتتاح اليوم:</span>
                                            <span id="dailyOpen" class="fw-bold">1.0860</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>أعلى سعر:</span>
                                            <span id="dailyHigh" class="fw-bold">1.0890</span>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>أدنى سعر:</span>
                                            <span id="dailyLow" class="fw-bold">1.0845</span>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="d-flex justify-content-between">
                                            <span>الحجم:</span>
                                            <span id="dailyVolume" class="fw-bold">12.5K</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interactive Chart Area -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>الرسم البياني التفاعلي</h2>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" id="toggleIndicatorsBtn" data-bs-toggle="modal" data-bs-target="#indicatorsModal">
                                <i class="fas fa-chart-line me-1"></i>المؤشرات
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="togglePatternsBtn" data-bs-toggle="modal" data-bs-target="#patternsModal">
                                <i class="fas fa-shapes me-1"></i>الأنماط
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="toggleSmcBtn" data-bs-toggle="modal" data-bs-target="#smcModal">
                                <i class="fas fa-layer-group me-1"></i>SMC/ICT
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="chartContainer" style="position: relative; height: 400px;">
                            <canvas id="priceChart" width="400" height="400"></canvas>
                        </div>
                        <div class="d-flex justify-content-center mt-3">
                            <div class="btn-group" role="group" aria-label="Chart Type">
                                <button type="button" class="btn btn-outline-primary active" id="candlestickBtn">شموع</button>
                                <button type="button" class="btn btn-outline-primary" id="lineChartBtn">خط</button>
                                <button type="button" class="btn btn-outline-primary" id="areaChartBtn">منطقة</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>التقويم الاقتصادي</h2>
                        <button class="btn btn-sm btn-outline-primary" id="refreshCalendarBtn">
                            <i class="fas fa-sync-alt me-1"></i>تحديث
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h5 id="calendarDate" class="mb-0">الأربعاء، 5 يوليو 2023</h5>
                            <div>
                                <select class="form-select form-select-sm" id="calendarFilter">
                                    <option value="all">جميع الأحداث</option>
                                    <option value="high">تأثير عالي فقط</option>
                                    <option value="medium">تأثير متوسط فقط</option>
                                    <option value="low">تأثير منخفض فقط</option>
                                </select>
                            </div>
                        </div>
                        <div id="calendarStatus"></div>
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>الوقت</th>
                                        <th>العملة</th>
                                        <th>الحدث</th>
                                        <th>التأثير</th>
                                        <th>التوقعات</th>
                                    </tr>
                                </thead>
                                <tbody id="calendarBody">
                                    <!-- Economic events will be loaded here -->
                                    <tr data-importance="high" class="table-danger">
                                        <td>08:30</td>
                                        <td>USD</td>
                                        <td>الوظائف غير الزراعية</td>
                                        <td><span class="badge bg-danger">عالي</span></td>
                                        <td>200K</td>
                                    </tr>
                                    <tr data-importance="high" class="table-danger">
                                        <td>10:00</td>
                                        <td>EUR</td>
                                        <td>قرار سعر الفائدة للبنك المركزي الأوروبي</td>
                                        <td><span class="badge bg-danger">عالي</span></td>
                                        <td>4.50%</td>
                                    </tr>
                                    <tr data-importance="medium" class="table-warning">
                                        <td>14:00</td>
                                        <td>USD</td>
                                        <td>مؤشر ISM للتصنيع</td>
                                        <td><span class="badge bg-warning text-dark">متوسط</span></td>
                                        <td>49.8</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trading Signals Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>إشارات التداول</h2>
                        <div class="btn-group">
                            <button class="btn btn-sm btn-outline-primary" id="signalHistoryBtn" data-bs-toggle="modal" data-bs-target="#signalHistoryModal">
                                <i class="fas fa-history me-1"></i>سجل الإشارات
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="exportSignalsBtn">
                                <i class="fas fa-file-export me-1"></i>تصدير
                            </button>
                            <button class="btn btn-sm btn-outline-primary" id="importSignalsBtn" data-bs-toggle="modal" data-bs-target="#importSignalsModal">
                                <i class="fas fa-file-import me-1"></i>استيراد
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <ul class="nav nav-tabs mb-3" id="signalsTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="current-signals-tab" data-bs-toggle="tab" data-bs-target="#current-signals" type="button" role="tab" aria-controls="current-signals" aria-selected="true">الإشارات الحالية</button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="signal-analytics-tab" data-bs-toggle="tab" data-bs-target="#signal-analytics" type="button" role="tab" aria-controls="signal-analytics" aria-selected="false">تحليلات الأداء</button>
                            </li>
                        </ul>
                        <div class="tab-content" id="signalsTabContent">
                            <div class="tab-pane fade show active" id="current-signals" role="tabpanel" aria-labelledby="current-signals-tab">
                                <div id="signalsContainer">
                                    <div class="alert alert-success">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <h4>إشارة شراء</h4>
                                            <div>
                                                <span class="badge bg-info">يورو/دولار</span>
                                                <span class="badge bg-secondary">H1</span>
                                                <button class="btn btn-sm btn-outline-success ms-2" data-signal-id="1" title="تتبع الإشارة">
                                                    <i class="fas fa-plus-circle"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <p><strong>نقطة الدخول:</strong> 1.0880</p>
                                        <p><strong>وقف الخسارة:</strong> 1.0850 <span class="text-muted">(30 نقطة)</span></p>
                                        <p><strong>جني الأرباح:</strong> 1.0930 <span class="text-muted">(50 نقطة)</span></p>
                                        <p><strong>نسبة المخاطرة/العائد:</strong> <span class="badge bg-success">1:1.67</span></p>
                                        <p><strong>التحليل:</strong> اخترق السعر فوق المتوسط المتحرك 200 مع زيادة في الحجم. مؤشر القوة النسبية يظهر زخمًا صاعدًا عند 65.</p>
                                        <div class="progress mt-2" style="height: 5px;">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: 75%;" aria-valuenow="75" aria-valuemin="0" aria-valuemax="100" title="ثقة الإشارة: 75%"></div>
                                        </div>
                                        <small class="text-muted">تم إنشاؤها: اليوم 10:30 صباحًا</small>
                                    </div>

                                    <div class="alert alert-danger mt-3">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <h4>إشارة بيع (سيناريو بديل)</h4>
                                            <div>
                                                <span class="badge bg-info">يورو/دولار</span>
                                                <span class="badge bg-secondary">H1</span>
                                                <button class="btn btn-sm btn-outline-success ms-2" data-signal-id="2" title="تتبع الإشارة">
                                                    <i class="fas fa-plus-circle"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <p><strong>نقطة الدخول:</strong> 1.0840</p>
                                        <p><strong>وقف الخسارة:</strong> 1.0870 <span class="text-muted">(30 نقطة)</span></p>
                                        <p><strong>جني الأرباح:</strong> 1.0790 <span class="text-muted">(50 نقطة)</span></p>
                                        <p><strong>نسبة المخاطرة/العائد:</strong> <span class="badge bg-success">1:1.67</span></p>
                                        <p><strong>التحليل:</strong> إذا فشل السعر في الحفاظ على مستوى الدعم 1.0850، توقع انعكاسًا. مؤشر MACD يظهر احتمالية تقاطع هبوطي.</p>
                                        <div class="progress mt-2" style="height: 5px;">
                                            <div class="progress-bar bg-warning" role="progressbar" style="width: 45%;" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100" title="ثقة الإشارة: 45%"></div>
                                        </div>
                                        <small class="text-muted">تم إنشاؤها: اليوم 10:35 صباحًا</small>
                                    </div>
                                </div>
                            </div>
                            <div class="tab-pane fade" id="signal-analytics" role="tabpanel" aria-labelledby="signal-analytics-tab">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">نسبة الفوز</h5>
                                                <div class="display-4 fw-bold text-success">68%</div>
                                                <p class="text-muted">آخر 30 إشارة</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="card mb-3">
                                            <div class="card-body text-center">
                                                <h5 class="card-title">متوسط الربح/الخسارة</h5>
                                                <div class="display-4 fw-bold text-success">1.8</div>
                                                <p class="text-muted">نسبة الربح:الخسارة</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="card mb-3">
                                    <div class="card-body">
                                        <h5 class="card-title">أداء الإشارات حسب الزوج</h5>
                                        <div class="table-responsive">
                                            <table class="table table-sm">
                                                <thead>
                                                    <tr>
                                                        <th>الزوج</th>
                                                        <th>عدد الإشارات</th>
                                                        <th>نسبة الفوز</th>
                                                        <th>متوسط النقاط</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>يورو/دولار</td>
                                                        <td>24</td>
                                                        <td>75%</td>
                                                        <td>+42</td>
                                                    </tr>
                                                    <tr>
                                                        <td>جنيه/دولار</td>
                                                        <td>18</td>
                                                        <td>67%</td>
                                                        <td>+38</td>
                                                    </tr>
                                                    <tr>
                                                        <td>دولار/ين</td>
                                                        <td>15</td>
                                                        <td>60%</td>
                                                        <td>+25</td>
                                                    </tr>
                                                    <tr>
                                                        <td>الذهب</td>
                                                        <td>12</td>
                                                        <td>83%</td>
                                                        <td>+68</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                <div class="card">
                                    <div class="card-body">
                                        <h5 class="card-title">أداء الإشارات حسب نوع الإشارة</h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title">إشارات الشراء</h6>
                                                        <div class="text-center">
                                                            <div class="display-6 fw-bold text-success">72%</div>
                                                            <p class="text-muted">نسبة الفوز (36 إشارة)</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title">إشارات البيع</h6>
                                                        <div class="text-center">
                                                            <div class="display-6 fw-bold text-success">65%</div>
                                                            <p class="text-muted">نسبة الفوز (28 إشارة)</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Backtesting Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h2>اختبار الاستراتيجيات</h2>
                        <button class="btn btn-sm btn-outline-primary" id="saveStrategyBtn">
                            <i class="fas fa-save me-1"></i>حفظ الاستراتيجية
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">إعدادات الاختبار</h5>
                                    </div>
                                    <div class="card-body">
                                        <form id="backtestForm">
                                            <div class="mb-3">
                                                <label for="backtestSymbol" class="form-label">الزوج</label>
                                                <select class="form-select" id="backtestSymbol">
                                                    <option value="EURUSD">يورو/دولار (EUR/USD)</option>
                                                    <option value="GBPUSD">جنيه/دولار (GBP/USD)</option>
                                                    <option value="USDJPY">دولار/ين (USD/JPY)</option>
                                                    <option value="XAUUSD">الذهب (XAU/USD)</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="backtestTimeframe" class="form-label">الإطار الزمني</label>
                                                <select class="form-select" id="backtestTimeframe">
                                                    <option value="M15">15 دقيقة</option>
                                                    <option value="H1" selected>ساعة</option>
                                                    <option value="H4">4 ساعات</option>
                                                    <option value="D1">يومي</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="backtestPeriod" class="form-label">فترة الاختبار</label>
                                                <select class="form-select" id="backtestPeriod">
                                                    <option value="1M">شهر واحد</option>
                                                    <option value="3M" selected>3 أشهر</option>
                                                    <option value="6M">6 أشهر</option>
                                                    <option value="1Y">سنة</option>
                                                </select>
                                            </div>
                                            <div class="mb-3">
                                                <label for="backtestStrategy" class="form-label">الاستراتيجية</label>
                                                <select class="form-select" id="backtestStrategy">
                                                    <option value="macd_cross">تقاطع MACD</option>
                                                    <option value="rsi_overbought">ذروة الشراء/البيع RSI</option>
                                                    <option value="ma_cross">تقاطع المتوسطات المتحركة</option>
                                                    <option value="breakout">اختراق المستويات</option>
                                                    <option value="custom">استراتيجية مخصصة</option>
                                                </select>
                                            </div>
                                            <div id="strategyParams" class="mb-3">
                                                <div class="card bg-light">
                                                    <div class="card-body">
                                                        <h6 class="card-title">معلمات الاستراتيجية</h6>
                                                        <div class="mb-2">
                                                            <label for="param1" class="form-label">MACD (سريع)</label>
                                                            <input type="number" class="form-control form-control-sm" id="param1" value="12">
                                                        </div>
                                                        <div class="mb-2">
                                                            <label for="param2" class="form-label">MACD (بطيء)</label>
                                                            <input type="number" class="form-control form-control-sm" id="param2" value="26">
                                                        </div>
                                                        <div class="mb-2">
                                                            <label for="param3" class="form-label">MACD (إشارة)</label>
                                                            <input type="number" class="form-control form-control-sm" id="param3" value="9">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="mb-3">
                                                <label for="riskPerTrade" class="form-label">المخاطرة لكل صفقة</label>
                                                <div class="input-group">
                                                    <input type="number" class="form-control" id="riskPerTrade" value="2">
                                                    <span class="input-group-text">%</span>
                                                </div>
                                            </div>
                                            <button type="button" id="runBacktestBtn" class="btn btn-primary w-100">
                                                <i class="fas fa-play me-1"></i>تشغيل الاختبار
                                            </button>
                                        </form>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h5 class="mb-0">نتائج الاختبار</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="backtestResults">
                                            <div class="row mb-3">
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">صافي الربح</h6>
                                                            <div class="text-success fw-bold">+12.8%</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">نسبة الفوز</h6>
                                                            <div class="fw-bold">68%</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">عدد الصفقات</h6>
                                                            <div class="fw-bold">42</div>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3">
                                                    <div class="card text-center">
                                                        <div class="card-body py-2">
                                                            <h6 class="card-title mb-0">نسبة شارب</h6>
                                                            <div class="fw-bold">1.85</div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row mb-3">
                                                <div class="col-12">
                                                    <div class="card">
                                                        <div class="card-body p-2">
                                                            <canvas id="equityCurve" height="200"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="card">
                                                        <div class="card-header py-2 bg-light">
                                                            <h6 class="mb-0">إحصائيات الصفقات</h6>
                                                        </div>
                                                        <div class="card-body p-0">
                                                            <table class="table table-sm mb-0">
                                                                <tbody>
                                                                    <tr>
                                                                        <td>متوسط الربح</td>
                                                                        <td class="text-end text-success">+1.8%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>متوسط الخسارة</td>
                                                                        <td class="text-end text-danger">-0.9%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>نسبة الربح:الخسارة</td>
                                                                        <td class="text-end">2.0</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>أقصى تراجع</td>
                                                                        <td class="text-end text-danger">-5.2%</td>
                                                                    </tr>
                                                                    <tr>
                                                                        <td>متوسط مدة الصفقة</td>
                                                                        <td class="text-end">2.4 يوم</td>
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="card">
                                                        <div class="card-header py-2 bg-light">
                                                            <h6 class="mb-0">توزيع الصفقات</h6>
                                                        </div>
                                                        <div class="card-body p-2">
                                                            <canvas id="tradesDistribution" height="150"></canvas>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Technical Indicators & Support/Resistance Section -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h2>المؤشرات الفنية</h2>
                    </div>
                    <div class="card-body">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>المؤشر</th>
                                    <th>القيمة</th>
                                    <th>الإشارة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>مؤشر القوة النسبية (14)</td>
                                    <td>65.3</td>
                                    <td><span class="badge bg-success">شراء</span></td>
                                </tr>
                                <tr>
                                    <td>MACD (12,26,9)</td>
                                    <td>0.0023</td>
                                    <td><span class="badge bg-success">شراء</span></td>
                                </tr>
                                <tr>
                                    <td>المتوسط المتحرك (50)</td>
                                    <td>1.0840</td>
                                    <td><span class="badge bg-success">شراء</span></td>
                                </tr>
                                <tr>
                                    <td>المتوسط المتحرك (200)</td>
                                    <td>1.0820</td>
                                    <td><span class="badge bg-success">شراء</span></td>
                                </tr>
                                <tr>
                                    <td>مؤشر بولينجر</td>
                                    <td>العلوي: 1.0910</td>
                                    <td><span class="badge bg-warning text-dark">محايد</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h2>مستويات الدعم والمقاومة</h2>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h4>مستويات المقاومة</h4>
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-danger">R3: 1.0950</span>
                                <span class="badge bg-danger">R2: 1.0930</span>
                                <span class="badge bg-danger">R1: 1.0910</span>
                            </div>
                        </div>
                        <div>
                            <h4>مستويات الدعم</h4>
                            <div class="d-flex justify-content-between">
                                <span class="badge bg-primary">S1: 1.0850</span>
                                <span class="badge bg-primary">S2: 1.0830</span>
                                <span class="badge bg-primary">S3: 1.0810</span>
                            </div>
                        </div>
                        <div class="mt-4">
                            <h4>مستويات السعر الرئيسية</h4>
                            <p><strong>المحور اليومي:</strong> 1.0865</p>
                            <p><strong>أعلى سعر أسبوعي:</strong> 1.0925</p>
                            <p><strong>أدنى سعر أسبوعي:</strong> 1.0805</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Modal -->
    <div class="modal fade" id="settingsModal" tabindex="-1" aria-labelledby="settingsModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="settingsModalLabel"><i class="fas fa-cog me-2"></i>الإعدادات</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="إغلاق"></button>
                </div>
                <div class="modal-body">
                    <!-- Theme Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">إعدادات المظهر</h6>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="darkModeToggle">
                            <label class="form-check-label" for="darkModeToggle">الوضع الداكن</label>
                        </div>
                    </div>

                    <!-- Notification Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">إعدادات الإشعارات</h6>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="notificationToggle">
                            <label class="form-check-label" for="notificationToggle">
                                الإشعارات المنبثقة
                                <span id="notificationStatus" class="me-2 badge bg-secondary">غير محدد</span>
                            </label>

                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="signalNotifications" checked>
                            <label class="form-check-label" for="signalNotifications">
                                تنبيهات إشارات التداول
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="economicNotifications" checked>
                            <label class="form-check-label" for="economicNotifications">
                                تنبيهات التقويم الاقتصادي
                            </label>
                        </div>
                        <div class="form-check mb-2">
                            <input class="form-check-input" type="checkbox" id="priceNotifications" checked>
                            <label class="form-check-label" for="priceNotifications">
                                تنبيهات الأسعار
                            </label>
                        </div>
                    </div>

                    <!-- Voice Command Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">إعدادات الأوامر الصوتية</h6>
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" id="voiceCommandsToggle">
                            <label class="form-check-label" for="voiceCommandsToggle">
                                تمكين الأوامر الصوتية
                                <span id="voiceCommandsStatus" class="me-2 badge bg-secondary">غير محدد</span>
                            </label>
                        </div>
                        <div class="mt-3">
                            <p class="small text-muted">الأوامر الصوتية المتاحة:</p>
                            <ul class="small text-muted ps-3">
                                <li>عرض الرسم البياني</li>
                                <li>تغيير الإطار الزمني إلى [5 دقائق، 15 دقيقة، ...]</li>
                                <li>تغيير الرمز إلى [يورو، جنيه، ذهب، ...]</li>
                                <li>تحليل السوق</li>
                                <li>تبديل السمة</li>
                                <li>تحديث البيانات</li>
                            </ul>
                        </div>
                    </div>

                    <!-- Language Settings -->
                    <div class="mb-4">
                        <h6 class="border-bottom pb-2 mb-3">إعدادات اللغة</h6>
                        <div class="mb-3">
                            <label for="languageSelect" class="form-label">لغة الواجهة</label>
                            <select class="form-select" id="languageSelect">
                                <option value="ar" selected>العربية</option>
                                <option value="en">English (الإنجليزية)</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                    <button type="button" class="btn btn-primary" id="saveSettingsBtn">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Critical Scripts - Load First -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Chart.js with all required modules -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js@4.4.0/dist/chart.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.3.0/build/global/luxon.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.3.1/dist/chartjs-adapter-luxon.umd.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-chart-financial@0.1.1/dist/chartjs-chart-financial.min.js"></script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/nprogress/0.2.0/nprogress.min.js"></script>
    <script src="register-sw.min.js" type="module"></script>

    <!-- Report Generation Libraries -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

    <!-- API Key Manager and Market Data Service -->
    <script src="js/api-key-manager.js"></script>
    <script src="js/market-data-service.js"></script>
    <script src="js/technical-analysis.js"></script>
    <script src="js/chart.js"></script>

    <!-- Welcome Animation -->
    <script>
        // Configure NProgress
        if (typeof NProgress !== 'undefined') {
            NProgress.configure({
                showSpinner: false,
                minimum: 0.1,
                easing: 'ease',
                speed: 500
            });
        }

        // Add welcome animation for first-time visitors
        document.addEventListener('DOMContentLoaded', function() {
            // Start progress bar
            if (typeof NProgress !== 'undefined') {
                NProgress.start();
            }

            const isFirstVisit = !localStorage.getItem('visited');

            if (isFirstVisit) {
                // Mark as visited
                localStorage.setItem('visited', 'true');

                // Add animation class to body
                document.body.classList.add('first-visit');

                // Add animation to main elements
                const mainElements = document.querySelectorAll('.card');
                mainElements.forEach((el, index) => {
                    el.style.opacity = '0';
                    el.style.transform = 'translateY(20px)';
                    el.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    el.style.transitionDelay = (0.2 * index) + 's';

                    setTimeout(() => {
                        el.style.opacity = '1';
                        el.style.transform = 'translateY(0)';
                    }, 100);
                });
            }

            // Complete progress bar
            if (typeof NProgress !== 'undefined') {
                setTimeout(() => {
                    NProgress.done();
                }, 500);
            }
        });
    </script>

    <!-- Non-critical Scripts - Load Deferred -->
    <script src="./src/utils/theme-switcher.min.js" defer></script>
    <script src="./src/utils/chart.js" defer></script>
    <script src="./src/services/technical-analysis.js" defer></script>
    <script src="./src/utils/chart-integration.js" defer></script>
    <script src="./src/utils/advanced-chart.min.js" defer></script>

    <!-- Main Application Script -->
    <script src="js/app.js"></script>

    <!-- Chart Fix Script -->
    <script src="js/chart-fix.js"></script>

    <!-- Service Worker Registration -->
    <script src="./src/utils/register-sw.min.js" type="module"></script>

<!-- Fallback Content Handler -->
    <script>
        // Handle API failures with fallback content
        window.addEventListener('error', function(e) {
            if (e.target.tagName.toLowerCase() === 'script') {
                console.warn('Script loading error:', e.target.src);
                showFallbackContent();
            }
        });

        function showFallbackContent() {
            const fallbackElements = document.querySelectorAll('[data-fallback]');
            fallbackElements.forEach(el => {
                el.style.display = 'block';
            });

            const loadingElements = document.querySelectorAll('[data-loading]');
            loadingElements.forEach(el => {
                el.style.display = 'none';
            });
        }
    </script>

    <!-- Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-XXXXXXXXXX"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'G-XXXXXXXXXX', {
            'page_title': 'Trading Signals App - Arabic',
            'send_page_view': true
        });

        // Track user interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Track button clicks
            document.querySelectorAll('button').forEach(button => {
                button.addEventListener('click', function() {
                    if (this.id && window.gtag) {
                        gtag('event', 'button_click', {
                            'button_id': this.id,
                            'button_text': this.innerText
                        });
                    }
                });
            });

            // Track form submissions
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function() {
                    if (this.id && window.gtag) {
                        gtag('event', 'form_submit', {
                            'form_id': this.id
                        });
                    }
                });
            });
        });
    </script>

    <script>
        // Initialize settings when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Connect dark mode toggle to theme switcher
            const darkModeToggle = document.getElementById('darkModeToggle');
            if (darkModeToggle) {
                // Set initial state based on current theme
                const currentTheme = document.documentElement.getAttribute('data-theme');
                darkModeToggle.checked = currentTheme === 'dark';

                // Add event listener
                darkModeToggle.addEventListener('change', function() {
                    if (this.checked) {
                        document.documentElement.setAttribute('data-theme', 'dark');
                        localStorage.setItem('theme_preference', 'dark');
                    } else {
                        document.documentElement.setAttribute('data-theme', 'light');
                        localStorage.setItem('theme_preference', 'light');
                    }

                    // Add fade transition class to body
                    document.body.classList.add('fade-transition');

                    // Remove the class after animation completes
                    setTimeout(() => {
                        document.body.classList.remove('fade-transition');
                    }, 500);
                });
            }

            // Connect language selector
            const languageSelect = document.getElementById('languageSelect');
            if (languageSelect) {
                // Set initial value based on current language
                const currentLang = localStorage.getItem('language_preference') || 'ar';
                languageSelect.value = currentLang;

                // Add event listener to save settings button
                const saveSettingsBtn = document.getElementById('saveSettingsBtn');
                if (saveSettingsBtn) {
                    saveSettingsBtn.addEventListener('click', function() {
                        const newLang = languageSelect.value;
                        localStorage.setItem('language_preference', newLang);

                        // If language changed, reload page
                        if (newLang !== currentLang) {
                            if (newLang === 'en') {
                                window.location.href = 'index_en.html';
                            } else {
                                window.location.reload();
                            }
                        } else {
                            // Just close the modal
                            const modal = bootstrap.Modal.getInstance(document.getElementById('settingsModal'));
                            modal.hide();
                        }
                    });
                }
            }

            // Add settings button to header
            const header = document.querySelector('header');
            if (header) {
                const settingsBtn = document.createElement('button');
                settingsBtn.className = 'btn btn-outline-primary mt-2';
                settingsBtn.setAttribute('data-bs-toggle', 'modal');
                settingsBtn.setAttribute('data-bs-target', '#settingsModal');
                settingsBtn.innerHTML = '<i class="fas fa-cog me-1"></i>الإعدادات';
                header.appendChild(settingsBtn);
            }
        });
    </script>
</body>
</html>

