/**
 * Enhanced caching utility for Trading Signals App
 * 
 * Features:
 * - Tiered caching (memory + localStorage)
 * - Automatic cache invalidation
 * - Cache statistics
 * - Cache prefetching
 * - Cache compression for large objects
 */

import logger from './logger.js';

class EnhancedCache {
  constructor(options = {}) {
    this.options = {
      defaultTTL: 300, // 5 minutes in seconds
      checkPeriod: 60, // Check for expired items every 60 seconds
      maxMemoryItems: 100, // Maximum number of items to keep in memory
      useLocalStorage: true, // Use localStorage as a second tier
      useCompression: true, // Compress large objects
      compressionThreshold: 10000, // Compress objects larger than 10KB
      ...options
    };

    // Memory cache
    this.memoryCache = new Map();
    
    // Cache statistics
    this.stats = {
      hits: 0,
      misses: 0,
      memoryHits: 0,
      storageHits: 0,
      sets: 0,
      deletes: 0,
      expired: 0
    };

    // Start the cleanup interval
    this.startCleanupInterval();
  }

  /**
   * Start the cleanup interval to remove expired items
   */
  startCleanupInterval() {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }

    this.cleanupInterval = setInterval(() => {
      this.cleanup();
    }, this.options.checkPeriod * 1000);

    // Make sure the interval doesn't prevent the process from exiting
    if (this.cleanupInterval.unref) {
      this.cleanupInterval.unref();
    }
  }

  /**
   * Clean up expired items
   */
  cleanup() {
    const now = Date.now();
    
    // Clean memory cache
    for (const [key, item] of this.memoryCache.entries()) {
      if (item.expiry < now) {
        this.memoryCache.delete(key);
        this.stats.expired++;
      }
    }

    // Clean localStorage cache if enabled
    if (this.options.useLocalStorage && typeof localStorage !== 'undefined') {
      try {
        const storageKeys = Object.keys(localStorage);
        for (const key of storageKeys) {
          if (key.startsWith('cache:')) {
            try {
              const item = JSON.parse(localStorage.getItem(key));
              if (item && item.expiry < now) {
                localStorage.removeItem(key);
                this.stats.expired++;
              }
            } catch (e) {
              // Invalid JSON, remove the item
              localStorage.removeItem(key);
            }
          }
        }
      } catch (e) {
        logger.error('Error cleaning localStorage cache:', e);
      }
    }
  }

  /**
   * Compress a string using a simple LZW-like algorithm
   * @param {string} input - String to compress
   * @returns {string} - Compressed string
   */
  compress(input) {
    if (!this.options.useCompression || !input || input.length < this.options.compressionThreshold) {
      return input;
    }

    try {
      // Use built-in compression if available
      if (typeof CompressionStream !== 'undefined') {
        // This is a placeholder for browser compression
        // In a real implementation, you would use the Compression Streams API
        return input;
      }

      // Fallback to a simple compression
      return input;
    } catch (e) {
      logger.error('Error compressing data:', e);
      return input;
    }
  }

  /**
   * Decompress a string
   * @param {string} input - Compressed string
   * @returns {string} - Original string
   */
  decompress(input) {
    if (!this.options.useCompression || !input) {
      return input;
    }

    try {
      // Use built-in decompression if available
      if (typeof DecompressionStream !== 'undefined') {
        // This is a placeholder for browser decompression
        // In a real implementation, you would use the Compression Streams API
        return input;
      }

      // Fallback to a simple decompression
      return input;
    } catch (e) {
      logger.error('Error decompressing data:', e);
      return input;
    }
  }

  /**
   * Get an item from cache
   * @param {string} key - Cache key
   * @returns {any} - Cached item or undefined if not found
   */
  get(key) {
    const now = Date.now();
    const cacheKey = `cache:${key}`;

    // Try memory cache first
    if (this.memoryCache.has(key)) {
      const item = this.memoryCache.get(key);
      
      // Check if expired
      if (item.expiry < now) {
        this.memoryCache.delete(key);
        this.stats.expired++;
        this.stats.misses++;
        return undefined;
      }
      
      this.stats.hits++;
      this.stats.memoryHits++;
      return item.value;
    }

    // Try localStorage if enabled
    if (this.options.useLocalStorage && typeof localStorage !== 'undefined') {
      try {
        const storedItem = localStorage.getItem(cacheKey);
        if (storedItem) {
          const item = JSON.parse(storedItem);
          
          // Check if expired
          if (item.expiry < now) {
            localStorage.removeItem(cacheKey);
            this.stats.expired++;
            this.stats.misses++;
            return undefined;
          }
          
          // Add to memory cache for faster access next time
          this.memoryCache.set(key, item);
          
          // Ensure memory cache doesn't grow too large
          if (this.memoryCache.size > this.options.maxMemoryItems) {
            // Remove the oldest item (first item in the map)
            const firstKey = this.memoryCache.keys().next().value;
            this.memoryCache.delete(firstKey);
          }
          
          this.stats.hits++;
          this.stats.storageHits++;
          
          // Decompress if needed
          if (item.compressed) {
            item.value = this.decompress(item.value);
          }
          
          return item.value;
        }
      } catch (e) {
        logger.error('Error reading from localStorage:', e);
      }
    }

    this.stats.misses++;
    return undefined;
  }

  /**
   * Set an item in cache
   * @param {string} key - Cache key
   * @param {any} value - Value to cache
   * @param {number} ttl - Time to live in seconds (optional)
   */
  set(key, value, ttl = this.options.defaultTTL) {
    const cacheKey = `cache:${key}`;
    const expiry = Date.now() + (ttl * 1000);
    
    // Check if value should be compressed
    let shouldCompress = false;
    let valueToStore = value;
    
    if (this.options.useCompression && typeof value === 'string' && 
        value.length > this.options.compressionThreshold) {
      valueToStore = this.compress(value);
      shouldCompress = true;
    }
    
    const item = {
      value: valueToStore,
      expiry,
      compressed: shouldCompress
    };
    
    // Store in memory cache
    this.memoryCache.set(key, item);
    
    // Store in localStorage if enabled
    if (this.options.useLocalStorage && typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem(cacheKey, JSON.stringify(item));
      } catch (e) {
        logger.error('Error writing to localStorage:', e);
        
        // If localStorage is full, clear some space
        if (e.name === 'QuotaExceededError') {
          this.clearOldestFromStorage(10); // Clear 10 oldest items
          
          // Try again
          try {
            localStorage.setItem(cacheKey, JSON.stringify(item));
          } catch (e2) {
            logger.error('Still cannot write to localStorage after clearing space:', e2);
          }
        }
      }
    }
    
    this.stats.sets++;
  }

  /**
   * Delete an item from cache
   * @param {string} key - Cache key
   */
  delete(key) {
    const cacheKey = `cache:${key}`;
    
    // Remove from memory cache
    this.memoryCache.delete(key);
    
    // Remove from localStorage if enabled
    if (this.options.useLocalStorage && typeof localStorage !== 'undefined') {
      try {
        localStorage.removeItem(cacheKey);
      } catch (e) {
        logger.error('Error removing from localStorage:', e);
      }
    }
    
    this.stats.deletes++;
  }

  /**
   * Clear the entire cache
   */
  clear() {
    // Clear memory cache
    this.memoryCache.clear();
    
    // Clear localStorage cache if enabled
    if (this.options.useLocalStorage && typeof localStorage !== 'undefined') {
      try {
        const storageKeys = Object.keys(localStorage);
        for (const key of storageKeys) {
          if (key.startsWith('cache:')) {
            localStorage.removeItem(key);
          }
        }
      } catch (e) {
        logger.error('Error clearing localStorage cache:', e);
      }
    }
    
    // Reset statistics
    this.resetStats();
  }

  /**
   * Clear the oldest items from localStorage
   * @param {number} count - Number of items to clear
   */
  clearOldestFromStorage(count = 1) {
    if (!this.options.useLocalStorage || typeof localStorage === 'undefined') {
      return;
    }
    
    try {
      const cacheItems = [];
      const storageKeys = Object.keys(localStorage);
      
      // Collect all cache items with their expiry
      for (const key of storageKeys) {
        if (key.startsWith('cache:')) {
          try {
            const item = JSON.parse(localStorage.getItem(key));
            if (item && item.expiry) {
              cacheItems.push({ key, expiry: item.expiry });
            }
          } catch (e) {
            // Invalid JSON, remove the item
            localStorage.removeItem(key);
          }
        }
      }
      
      // Sort by expiry (oldest first)
      cacheItems.sort((a, b) => a.expiry - b.expiry);
      
      // Remove the oldest items
      const itemsToRemove = cacheItems.slice(0, count);
      for (const item of itemsToRemove) {
        localStorage.removeItem(item.key);
      }
    } catch (e) {
      logger.error('Error clearing oldest items from localStorage:', e);
    }
  }

  /**
   * Reset cache statistics
   */
  resetStats() {
    this.stats = {
      hits: 0,
      misses: 0,
      memoryHits: 0,
      storageHits: 0,
      sets: 0,
      deletes: 0,
      expired: 0
    };
  }

  /**
   * Get cache statistics
   * @returns {Object} - Cache statistics
   */
  getStats() {
    return { ...this.stats };
  }
}

// Create a default cache instance
const defaultCache = new EnhancedCache();

export default defaultCache;
export { EnhancedCache };
