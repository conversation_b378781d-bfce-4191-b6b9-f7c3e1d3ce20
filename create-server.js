/**
 * Simple HTTP server to serve static files without external dependencies
 */
const http = require('http');
const fs = require('fs');
const path = require('path');

// Set the port (default: 3000)
const PORT = process.env.PORT || 3000;

// Define content types for different file extensions
const contentTypes = {
  '.html': 'text/html',
  '.css': 'text/css',
  '.js': 'text/javascript',
  '.json': 'application/json',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.jpeg': 'image/jpeg',
  '.svg': 'image/svg+xml',
  '.ico': 'image/x-icon',
  '.txt': 'text/plain',
  '.gif': 'image/gif',
  '.ttf': 'font/ttf',
  '.woff': 'font/woff',
  '.woff2': 'font/woff2'
};

// Create the HTTP server
const server = http.createServer((req, res) => {
  console.log(`${req.method} ${req.url}`);
  
  // Get the URL path
  let urlPath = req.url;
  
  // Default to index_fixed.html if requesting the root path
  if (urlPath === '/' || urlPath === '') {
    urlPath = '/index_fixed.html';
  }
  
  // Remove query strings from URL
  urlPath = urlPath.split('?')[0];
  
  // Define potential file paths to check
  const potentialPaths = [
    path.join(__dirname, 'public', urlPath),
    path.join(__dirname, urlPath),
    path.join(__dirname, 'src', urlPath.substring(1))
  ];
  
  // Try to find the file in one of our potential directories
  let fileFound = false;
  
  for (const filePath of potentialPaths) {
    try {
      if (fs.existsSync(filePath) && fs.statSync(filePath).isFile()) {
        // Get the file extension
        const extname = path.extname(filePath).toLowerCase();
        
        // Set the content type based on file extension
        const contentType = contentTypes[extname] || 'application/octet-stream';
        
        // Read and serve the file
        fs.readFile(filePath, (err, content) => {
          if (err) {
            console.error(`Error reading file ${filePath}:`, err);
            res.writeHead(500);
            res.end(`Error reading file: ${err.message}`);
            return;
          }
          
          // Serve the file with appropriate content type
          res.writeHead(200, { 'Content-Type': contentType });
          res.end(content, 'utf-8');
          console.log(`✓ 200 Served: ${filePath}`);
        });
        
        fileFound = true;
        break;
      }
    } catch (err) {
      console.error(`Error checking file ${filePath}:`, err);
    }
  }
  
  // Return 404 if file not found in any of our directories
  if (!fileFound) {
    res.writeHead(404, { 'Content-Type': 'text/html' });
    res.end(`<html><body><h1>404 Not Found</h1><p>The requested URL ${urlPath} was not found on this server.</p></body></html>`);
    console.log(`✗ 404 Not Found: ${urlPath}`);
  }
});

// Start the server
server.listen(PORT, () => {
  console.log(`
==================================================
 Server running at http://localhost:${PORT}
 Press Ctrl+C to stop the server
 
 This server serves static files from the  
 following directories:
 - public/
 - root directory
 - src/
==================================================
  `);
}); 