require('dotenv').config();
const express = require('express');
const path = require('path');
const cors = require('cors');
const morgan = require('morgan');
const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const winston = require('winston');
require('winston-daily-rotate-file');
const Redis = require('ioredis');
const { connect, findResults, saveResult } = require('./db/mongo');
const config = require('./config');
const fallbackAPIs = require('./fallback/fallbackHandler');
const { calculateIndicators } = require('./utils/indicators');
const { generateSignals } = require('./utils/signals');
const { validateInputs } = require('./utils/validation');

// Configure Redis
const redis = new Redis({
  host: process.env.REDIS_HOST || 'localhost',
  port: process.env.REDIS_PORT || 6379,
  retryStrategy: (times) => Math.min(times * 50, 2000)
});

// Configure Winston logger with daily rotate
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  transports: [
    new winston.transports.DailyRotateFile({
      filename: 'logs/error-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      level: 'error',
      maxFiles: '14d'
    }),
    new winston.transports.DailyRotateFile({
      filename: 'logs/combined-%DATE%.log',
      datePattern: 'YYYY-MM-DD',
      maxFiles: '14d'
    })
  ]
});

if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: winston.format.combine(
      winston.format.colorize(),
      winston.format.simple()
    )
  }));
}

const app = express();
const PORT = process.env.PORT || 3000;

// Rate limiting
const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
  message: 'Too many requests from this IP, please try again later'
});

// Advanced security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-inline'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "https://api.example.com"]
    }
  },
  crossOriginEmbedderPolicy: true,
  crossOriginOpenerPolicy: true,
  crossOriginResourcePolicy: { policy: "cross-origin" }
}));

app.use(cors({
  origin: process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : '*',
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true,
  maxAge: 86400 // 24 hours
}));

app.use(compression({ level: 6 }));
app.use(morgan('combined', { stream: { write: message => logger.info(message.trim()) } }));
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public'), {
  maxAge: '1d',
  etag: true,
  lastModified: true
}));
app.use(limiter);

// Graceful MongoDB connection with exponential backoff
const connectWithRetry = async (retries = 5, initialDelay = 1000) => {
  for (let i = 0; i < retries; i++) {
    try {
      await connect();
      logger.info('Successfully connected to MongoDB');
      return;
    } catch (err) {
      const delay = initialDelay * Math.pow(2, i);
      logger.error(`MongoDB connection attempt ${i + 1}/${retries} failed:`, err);
      if (i < retries - 1) await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  throw new Error('Failed to connect to MongoDB after multiple attempts');
};

// Initialize services
const initializeServices = async () => {
  try {
    await connectWithRetry();
    await redis.ping();
    logger.info('Redis connection established');
  } catch (err) {
    logger.error('Service initialization failed:', err);
    process.exit(1);
  }
};

initializeServices();

// Advanced health check endpoint
app.get('/api/health', async (req, res) => {
  try {
    const mongoStatus = await mongoose.connection.db.admin().ping();
    const redisStatus = await redis.ping();
    
    res.json({
      status: 'ok',
      time: new Date(),
      services: {
        mongodb: mongoStatus ? 'connected' : 'disconnected',
        redis: redisStatus === 'PONG' ? 'connected' : 'disconnected'
      },
      uptime: process.uptime(),
      memory: process.memoryUsage(),
      environment: process.env.NODE_ENV,
      version: process.env.npm_package_version
    });
  } catch (err) {
    res.status(503).json({ status: 'error', message: err.message });
  }
});

// Enhanced market data endpoint with caching and validation
app.get('/api/market/:type/:symbol', validateInputs, async (req, res) => {
  const { type, symbol } = req.params;
  const timeframe = req.query.timeframe || 'D1';
  const cacheKey = `market:${type}:${symbol}:${timeframe}`;

  try {
    // Check Redis cache first
    const cachedData = await redis.get(cacheKey);
    if (cachedData) {
      return res.json(JSON.parse(cachedData));
    }

    // Get fresh data using fallback APIs
    const result = await fallbackAPIs(config.API_PRIORITY[type.toLowerCase()], {
      type: type.toLowerCase(),
      symbol: symbol.toUpperCase(),
      timeframe
    });

    // Cache the result
    await redis.setex(cacheKey, 300, JSON.stringify(result)); // 5 minute cache

    // Save to MongoDB for historical reference
    await saveResult(type, {
      query: { type, symbol: symbol.toUpperCase(), timeframe },
      result,
      date: new Date()
    });

    res.json(result);
  } catch (err) {
    logger.error('Market data fetch error:', err);
    res.status(503).json({
      error: 'Service temporarily unavailable',
      message: 'Unable to fetch market data',
      retryAfter: 60
    });
  }
});

// Advanced technical indicators endpoint
app.get('/api/indicators/:type/:symbol', validateInputs, async (req, res) => {
  try {
    const { type, symbol } = req.params;
    const timeframe = req.query.timeframe || 'D1';
    const indicators = req.query.indicators ? 
      req.query.indicators.split(',').map(i => i.trim().toLowerCase()) : 
      ['rsi', 'macd', 'sma', 'ema', 'bbands'];

    const cacheKey = `indicators:${type}:${symbol}:${timeframe}:${indicators.join(':')}`;
    
    // Check cache
    const cachedData = await redis.get(cacheKey);
    if (cachedData) {
      return res.json(JSON.parse(cachedData));
    }

    // Get market data
    const marketData = await fallbackAPIs(config.API_PRIORITY[type], {
      type,
      symbol: symbol.toUpperCase(),
      timeframe
    });

    // Calculate indicators
    const results = await calculateIndicators(marketData, indicators);
    
    // Cache results
    await redis.setex(cacheKey, 300, JSON.stringify(results));
    
    res.json(results);
  } catch (err) {
    logger.error('Indicator calculation error:', err);
    res.status(500).json({ error: 'Failed to calculate indicators', details: err.message });
  }
});

// Advanced signals endpoint with ML integration
app.get('/api/signals/:type/:symbol', validateInputs, async (req, res) => {
  try {
    const { type, symbol } = req.params;
    const timeframe = req.query.timeframe || 'D1';
    
    const cacheKey = `signals:${type}:${symbol}:${timeframe}`;
    
    // Check cache
    const cachedSignals = await redis.get(cacheKey);
    if (cachedSignals) {
      return res.json(JSON.parse(cachedSignals));
    }

    // Get market data and indicators
    const [marketData, indicators] = await Promise.all([
      fallbackAPIs(config.API_PRIORITY[type], {
        type,
        symbol: symbol.toUpperCase(),
        timeframe
      }),
      calculateIndicators(marketData, ['rsi', 'macd', 'bbands'])
    ]);

    // Generate signals using ML models
    const signals = await generateSignals(marketData, indicators);
    
    // Cache signals
    await redis.setex(cacheKey, 300, JSON.stringify(signals));
    
    res.json(signals);
  } catch (err) {
    logger.error('Signal generation error:', err);
    res.status(500).json({ error: 'Failed to generate signals', details: err.message });
  }
});

// Custom error handling
app.use((req, res) => {
  res.status(404).sendFile(path.join(__dirname, 'public', '404.html'));
});

app.use((err, req, res, next) => {
  logger.error('Unhandled error:', {
    error: err.message,
    stack: err.stack,
    request: {
      url: req.url,
      method: req.method,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query
    }
  });

  res.status(err.status || 500).json({
    error: process.env.NODE_ENV === 'production' ? 'Internal server error' : err.message,
    requestId: req.id,
    timestamp: new Date().toISOString()
  });
});

// Graceful shutdown handling
const gracefulShutdown = async () => {
  logger.info('Initiating graceful shutdown...');
  
  server.close(async () => {
    logger.info('HTTP server closed');
    
    try {
      await mongoose.connection.close();
      logger.info('MongoDB connection closed');
      
      await redis.quit();
      logger.info('Redis connection closed');
      
      process.exit(0);
    } catch (err) {
      logger.error('Error during shutdown:', err);
      process.exit(1);
    }
  });

  // Force shutdown after 30 seconds
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 30000);
};

const server = app.listen(PORT, () => {
  logger.info(`Server running at http://localhost:${PORT} in ${process.env.NODE_ENV} mode`);
});

// Handle various shutdown signals
process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);
process.on('uncaughtException', (err) => {
  logger.error('Uncaught Exception:', err);
  gracefulShutdown();
});
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
});