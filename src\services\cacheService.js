/**
 * Cache Service for MongoDB Data
 * 
 * This service provides caching functionality for MongoDB data.
 */

const NodeCache = require('node-cache');
const logger = require('../utils/logger');

// Try to load Redis client if available
let redisClient;
try {
  const redis = require('redis');
  if (process.env.REDIS_URL) {
    redisClient = redis.createClient({
      url: process.env.REDIS_URL
    });
    
    redisClient.on('error', (err) => {
      logger.error('Redis Client Error', err);
      // Fallback to in-memory cache if Redis fails
      redisClient = null;
    });
    
    // Connect to Redis
    (async () => {
      try {
        await redisClient.connect();
        logger.info('Connected to Redis successfully');
      } catch (err) {
        logger.error('Failed to connect to Redis', err);
        redisClient = null;
      }
    })();
  }
} catch (err) {
  logger.info('Redis not available, using in-memory cache only');
}

// Default TTL values (in seconds)
const DEFAULT_TTL = {
  SHORT: 60,        // 1 minute
  MEDIUM: 300,      // 5 minutes
  LONG: 1800,       // 30 minutes
  VERY_LONG: 3600   // 1 hour
};

// Cache instance
const memoryCache = new NodeCache({
  stdTTL: DEFAULT_TTL.MEDIUM,
  checkperiod: 60,
  useClones: false  // For better performance with large objects
});

/**
 * Generate cache key
 * @param {string} prefix - Key prefix
 * @param {Object} params - Parameters to include in the key
 * @returns {string} Cache key
 */
function generateKey(prefix, params = {}) {
  const paramString = Object.entries(params)
    .filter(([_, value]) => value !== undefined && value !== null)
    .map(([key, value]) => `${key}:${typeof value === 'object' ? JSON.stringify(value) : value}`)
    .join('|');
  
  return `${prefix}${paramString ? `|${paramString}` : ''}`;
}

/**
 * Get data from cache
 * @param {string} key - Cache key
 * @returns {Promise<any>} Cached data or undefined if not found
 */
async function get(key) {
  try {
    // Try Redis first if available
    if (redisClient && redisClient.isReady) {
      const data = await redisClient.get(key);
      if (data) {
        logger.debug(`Redis cache hit for key: ${key}`);
        return JSON.parse(data);
      }
      logger.debug(`Redis cache miss for key: ${key}`);
    }
    
    // Fall back to memory cache
    const data = memoryCache.get(key);
    if (data) {
      logger.debug(`Memory cache hit for key: ${key}`);
    } else {
      logger.debug(`Memory cache miss for key: ${key}`);
    }
    return data;
  } catch (error) {
    logger.error(`Error getting data from cache for key: ${key}`, error);
    return undefined;
  }
}

/**
 * Set data in cache
 * @param {string} key - Cache key
 * @param {any} data - Data to cache
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<boolean>} True if successful
 */
async function set(key, data, ttl = DEFAULT_TTL.MEDIUM) {
  try {
    logger.debug(`Setting cache for key: ${key} with TTL: ${ttl}s`);
    
    // Set in Redis if available
    if (redisClient && redisClient.isReady) {
      await redisClient.set(key, JSON.stringify(data), {
        EX: ttl
      });
    }
    
    // Also set in memory cache
    return memoryCache.set(key, data, ttl);
  } catch (error) {
    logger.error(`Error setting data in cache for key: ${key}`, error);
    return false;
  }
}

/**
 * Delete data from cache
 * @param {string} key - Cache key
 * @returns {Promise<number>} Number of deleted entries
 */
async function del(key) {
  try {
    logger.debug(`Deleting cache for key: ${key}`);
    
    // Delete from Redis if available
    if (redisClient && redisClient.isReady) {
      await redisClient.del(key);
    }
    
    // Also delete from memory cache
    return memoryCache.del(key);
  } catch (error) {
    logger.error(`Error deleting data from cache for key: ${key}`, error);
    return 0;
  }
}

/**
 * Delete multiple keys from cache
 * @param {Array<string>} keys - Array of cache keys
 * @returns {number} Number of deleted entries
 */
function delMultiple(keys) {
  try {
    logger.debug(`Deleting cache for keys: ${keys.join(', ')}`);
    return memoryCache.del(keys);
  } catch (error) {
    logger.error(`Error deleting multiple keys from cache`, error);
    return 0;
  }
}

/**
 * Flush entire cache
 * @returns {void}
 */
function flush() {
  try {
    logger.debug('Flushing entire cache');
    memoryCache.flushAll();
  } catch (error) {
    logger.error('Error flushing cache', error);
  }
}

/**
 * Get cache statistics
 * @returns {Object} Cache statistics
 */
function getStats() {
  try {
    return memoryCache.getStats();
  } catch (error) {
    logger.error('Error getting cache statistics', error);
    return {};
  }
}

/**
 * Get all cache keys
 * @returns {Array<string>} Array of cache keys
 */
function getKeys() {
  try {
    return memoryCache.keys();
  } catch (error) {
    logger.error('Error getting cache keys', error);
    return [];
  }
}

/**
 * Get or set cache value with callback
 * @param {string} key - Cache key
 * @param {Function} callback - Function to generate data if not in cache
 * @param {number} ttl - Time to live in seconds
 * @returns {Promise<any>} Data from cache or callback
 */
async function getOrSet(key, callback, ttl = DEFAULT_TTL.MEDIUM) {
  try {
    // Try to get from cache first
    const cachedData = await get(key);
    if (cachedData !== undefined) {
      return cachedData;
    }
    
    // If not in cache, call the callback to generate data
    logger.debug(`Cache miss for key: ${key}, calling callback`);
    const data = await callback();
    
    // Store in cache
    await set(key, data, ttl);
    
    return data;
  } catch (error) {
    logger.error(`Error in getOrSet for key: ${key}`, error);
    throw error;
  }
}

/**
 * Invalidate cache keys by pattern
 * @param {string} pattern - Key pattern to match
 * @returns {number} Number of deleted entries
 */
function invalidateByPattern(pattern) {
  try {
    const keys = memoryCache.keys();
    const matchingKeys = keys.filter(key => key.includes(pattern));
    
    if (matchingKeys.length > 0) {
      logger.debug(`Invalidating ${matchingKeys.length} cache keys matching pattern: ${pattern}`);
      return memoryCache.del(matchingKeys);
    }
    
    return 0;
  } catch (error) {
    logger.error(`Error invalidating cache by pattern: ${pattern}`, error);
    return 0;
  }
}

module.exports = {
  DEFAULT_TTL,
  generateKey,
  get,
  set,
  del,
  delMultiple,
  flush,
  getStats,
  getKeys,
  getOrSet,
  invalidateByPattern
};
