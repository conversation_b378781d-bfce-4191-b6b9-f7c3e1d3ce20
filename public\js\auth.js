/**
 * Authentication Module for Trading Signals App
 * 
 * This file handles user authentication (registration and login)
 * and manages the authentication state.
 */

// API base URL
const API_BASE_URL = '/api/mongodb';

// Authentication state
let authState = {
  isAuthenticated: false,
  user: null,
  token: null
};

// Initialize authentication from localStorage
function initAuth() {
  const token = localStorage.getItem('auth_token');
  const user = localStorage.getItem('auth_user');
  
  if (token && user) {
    try {
      authState.token = token;
      authState.user = JSON.parse(user);
      authState.isAuthenticated = true;
      
      // Update UI based on authentication state
      updateAuthUI();
      
      // Verify token validity with the server
      verifyToken();
      
      return true;
    } catch (error) {
      console.error('Error parsing stored user data:', error);
      clearAuth();
    }
  }
  
  updateAuthUI();
  return false;
}

// Register a new user
async function registerUser(userData) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(userData)
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Registration failed');
    }
    
    // Show success message
    showMessage('success', 'Registration successful! You can now log in.');
    
    // Switch to login form
    showLoginForm();
    
    return data;
  } catch (error) {
    showMessage('error', error.message);
    throw error;
  }
}

// Login a user
async function loginUser(credentials) {
  try {
    const response = await fetch(`${API_BASE_URL}/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(credentials)
    });
    
    const data = await response.json();
    
    if (!response.ok) {
      throw new Error(data.message || 'Login failed');
    }
    
    // Store authentication data
    authState.token = data.token;
    authState.user = data.user;
    authState.isAuthenticated = true;
    
    // Save to localStorage
    localStorage.setItem('auth_token', data.token);
    localStorage.setItem('auth_user', JSON.stringify(data.user));
    
    // Update UI
    updateAuthUI();
    
    // Show success message
    showMessage('success', 'Login successful!');
    
    // Redirect to dashboard or reload page
    setTimeout(() => {
      window.location.href = '/dashboard.html';
    }, 1000);
    
    return data;
  } catch (error) {
    showMessage('error', error.message);
    throw error;
  }
}

// Logout the current user
function logoutUser() {
  // Clear authentication data
  clearAuth();
  
  // Show success message
  showMessage('success', 'Logout successful!');
  
  // Redirect to home page
  setTimeout(() => {
    window.location.href = '/';
  }, 1000);
}

// Clear authentication data
function clearAuth() {
  authState.token = null;
  authState.user = null;
  authState.isAuthenticated = false;
  
  // Remove from localStorage
  localStorage.removeItem('auth_token');
  localStorage.removeItem('auth_user');
  
  // Update UI
  updateAuthUI();
}

// Verify token validity with the server
async function verifyToken() {
  if (!authState.token) return false;
  
  try {
    const response = await fetch(`${API_BASE_URL}/auth/me`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authState.token}`
      }
    });
    
    if (!response.ok) {
      // Token is invalid or expired
      clearAuth();
      return false;
    }
    
    const userData = await response.json();
    
    // Update user data
    authState.user = userData;
    localStorage.setItem('auth_user', JSON.stringify(userData));
    
    return true;
  } catch (error) {
    console.error('Error verifying token:', error);
    clearAuth();
    return false;
  }
}

// Get authentication headers for API requests
function getAuthHeaders() {
  return {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${authState.token}`
  };
}

// Update UI based on authentication state
function updateAuthUI() {
  const authNav = document.getElementById('auth-nav');
  const authForms = document.getElementById('auth-forms');
  const userInfo = document.getElementById('user-info');
  
  if (!authNav) return; // Not on a page with auth elements
  
  if (authState.isAuthenticated) {
    // User is logged in
    authNav.innerHTML = `
      <button id="logout-btn" class="btn btn-outline">Logout</button>
    `;
    
    if (userInfo) {
      userInfo.innerHTML = `
        <p>Welcome, <strong>${authState.user.username || authState.user.email}</strong>!</p>
      `;
      userInfo.style.display = 'block';
    }
    
    if (authForms) {
      authForms.style.display = 'none';
    }
    
    // Add logout event listener
    document.getElementById('logout-btn').addEventListener('click', logoutUser);
    
  } else {
    // User is not logged in
    authNav.innerHTML = `
      <button id="login-nav-btn" class="btn btn-outline">Login</button>
      <button id="register-nav-btn" class="btn btn-primary">Register</button>
    `;
    
    if (userInfo) {
      userInfo.style.display = 'none';
    }
    
    if (authForms) {
      authForms.style.display = 'block';
    }
    
    // Add event listeners
    document.getElementById('login-nav-btn').addEventListener('click', showLoginForm);
    document.getElementById('register-nav-btn').addEventListener('click', showRegisterForm);
  }
}

// Show login form
function showLoginForm() {
  const authForms = document.getElementById('auth-forms');
  if (!authForms) return;
  
  authForms.innerHTML = `
    <div class="auth-form-container">
      <h2>Login</h2>
      <form id="login-form">
        <div class="form-group">
          <label for="login-email">Email</label>
          <input type="email" id="login-email" required>
        </div>
        <div class="form-group">
          <label for="login-password">Password</label>
          <input type="password" id="login-password" required>
        </div>
        <button type="submit" class="btn btn-primary">Login</button>
      </form>
      <p>Don't have an account? <a href="#" id="show-register">Register</a></p>
      <div id="auth-message"></div>
    </div>
  `;
  
  // Add event listeners
  document.getElementById('login-form').addEventListener('submit', handleLogin);
  document.getElementById('show-register').addEventListener('click', (e) => {
    e.preventDefault();
    showRegisterForm();
  });
}

// Show registration form
function showRegisterForm() {
  const authForms = document.getElementById('auth-forms');
  if (!authForms) return;
  
  authForms.innerHTML = `
    <div class="auth-form-container">
      <h2>Register</h2>
      <form id="register-form">
        <div class="form-group">
          <label for="register-username">Username</label>
          <input type="text" id="register-username" required>
        </div>
        <div class="form-group">
          <label for="register-email">Email</label>
          <input type="email" id="register-email" required>
        </div>
        <div class="form-group">
          <label for="register-password">Password</label>
          <input type="password" id="register-password" required minlength="8">
        </div>
        <div class="form-group">
          <label for="register-fullname">Full Name</label>
          <input type="text" id="register-fullname">
        </div>
        <button type="submit" class="btn btn-primary">Register</button>
      </form>
      <p>Already have an account? <a href="#" id="show-login">Login</a></p>
      <div id="auth-message"></div>
    </div>
  `;
  
  // Add event listeners
  document.getElementById('register-form').addEventListener('submit', handleRegister);
  document.getElementById('show-login').addEventListener('click', (e) => {
    e.preventDefault();
    showLoginForm();
  });
}

// Handle login form submission
async function handleLogin(e) {
  e.preventDefault();
  
  const email = document.getElementById('login-email').value;
  const password = document.getElementById('login-password').value;
  
  try {
    await loginUser({ email, password });
  } catch (error) {
    console.error('Login error:', error);
  }
}

// Handle registration form submission
async function handleRegister(e) {
  e.preventDefault();
  
  const username = document.getElementById('register-username').value;
  const email = document.getElementById('register-email').value;
  const password = document.getElementById('register-password').value;
  const fullName = document.getElementById('register-fullname').value;
  
  try {
    await registerUser({ username, email, password, fullName });
  } catch (error) {
    console.error('Registration error:', error);
  }
}

// Show message in the auth form
function showMessage(type, message) {
  const messageElement = document.getElementById('auth-message');
  if (!messageElement) return;
  
  messageElement.className = `message ${type}`;
  messageElement.textContent = message;
  
  // Clear message after 5 seconds
  setTimeout(() => {
    messageElement.textContent = '';
    messageElement.className = 'message';
  }, 5000);
}

// Initialize when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initAuth();
  
  // Show login form by default if not authenticated
  if (!authState.isAuthenticated) {
    showLoginForm();
  }
});

// Export functions for use in other modules
window.Auth = {
  login: loginUser,
  register: registerUser,
  logout: logoutUser,
  getAuthHeaders,
  isAuthenticated: () => authState.isAuthenticated,
  getCurrentUser: () => authState.user
};
